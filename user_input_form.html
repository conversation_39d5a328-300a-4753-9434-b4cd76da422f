<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thê<PERSON><PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            padding: 30px;
            text-align: center;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .form-container {
            padding: 40px;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e1e5e9;
            display: flex;
            align-items: center;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            margin-right: 15px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row.single {
            grid-template-columns: 1fr;
        }

        .form-group {
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .required {
            color: #e74c3c;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .user-type-toggle {
            display: flex;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 20px;
        }

        .user-type-toggle label {
            flex: 1;
            text-align: center;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 0;
        }

        .user-type-toggle input[type="radio"] {
            display: none;
        }

        .user-type-toggle input[type="radio"]:checked + label {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            box-shadow: 0 2px 10px rgba(79, 172, 254, 0.3);
        }

        .selection-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .selection-group h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .search-input {
            position: relative;
            margin-bottom: 15px;
        }

        .search-input input {
            padding-left: 40px;
            background: white;
        }

        .search-input::before {
            content: '🔍';
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.1rem;
            z-index: 1;
        }

        .options-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: white;
        }

        .option-item {
            padding: 12px 15px;
            border-bottom: 1px solid #f1f3f4;
            cursor: pointer;
            transition: background 0.2s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .option-item:hover {
            background: #f8f9fa;
        }

        .option-item.selected {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .option-item:last-child {
            border-bottom: none;
        }

        .option-details {
            font-size: 0.85rem;
            opacity: 0.7;
        }

        .selected-info {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            display: none;
        }

        .selected-info.show {
            display: block;
        }

        .selected-info h4 {
            color: #2e7d32;
            margin-bottom: 8px;
        }

        .selected-info p {
            margin-bottom: 5px;
            color: #555;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 40px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .validation-message {
            color: #e74c3c;
            font-size: 0.9rem;
            margin-top: 5px;
            display: none;
        }

        .validation-message.show {
            display: block;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
            display: none;
        }

        .success-message.show {
            display: block;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .container {
                margin: 10px;
            }

            .form-container {
                padding: 20px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Thêm Nhân Viên Mới</h1>
            <p>Nhập thông tin cơ bản để tạo hồ sơ nhân viên</p>
        </div>

        <div class="form-container">
            <div class="success-message" id="successMessage">
                Thêm nhân viên thành công! Thông tin đã được lưu vào hệ thống.
            </div>

            <form id="userForm">
                <!-- User Type Selection -->
                <div class="form-section">
                    <div class="section-title">Loại Nhân Viên</div>
                    <div class="user-type-toggle">
                        <input type="radio" id="manualUser" name="user_entry_type" value="manual" checked>
                        <label for="manualUser">Nhập Thủ Công</label>
                        
                        <input type="radio" id="systemUser" name="user_entry_type" value="system">
                        <label for="systemUser">Tạo Bởi Hệ Thống</label>
                    </div>
                    <p style="color: #666; font-size: 0.9rem; text-align: center;">
                        Chọn "Nhập Thủ Công" cho nhân viên được thêm bởi admin, "Tạo Bởi Hệ Thống" cho nhân viên từ tích hợp tự động
                    </p>
                </div>

                <!-- Basic Information -->
                <div class="form-section">
                    <div class="section-title">Thông Tin Cơ Bản</div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">Tên <span class="required">*</span></label>
                            <input type="text" id="firstName" name="first_name" required>
                            <div class="validation-message" id="firstNameError">Vui lòng nhập tên</div>
                        </div>
                        <div class="form-group">
                            <label for="lastName">Họ <span class="required">*</span></label>
                            <input type="text" id="lastName" name="last_name" required>
                            <div class="validation-message" id="lastNameError">Vui lòng nhập họ</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="middleName">Tên Đệm</label>
                            <input type="text" id="middleName" name="middle_name">
                        </div>
                        <div class="form-group">
                            <label for="employeeCode">Mã Nhân Viên</label>
                            <input type="text" id="employeeCode" name="employee_code" placeholder="Tự động tạo nếu để trống">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="primaryEmail">Email Chính <span class="required">*</span></label>
                            <input type="email" id="primaryEmail" name="primary_email" required>
                            <div class="validation-message" id="emailError">Vui lòng nhập email hợp lệ</div>
                        </div>
                        <div class="form-group">
                            <label for="phoneNumber">Số Điện Thoại <span class="required">*</span></label>
                            <input type="tel" id="phoneNumber" name="phone_number" required>
                            <div class="validation-message" id="phoneError">Vui lòng nhập số điện thoại</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="gender">Giới Tính <span class="required">*</span></label>
                            <select id="gender" name="gender" required>
                                <option value="">Chọn giới tính</option>
                                <option value="0">Nam</option>
                                <option value="1">Nữ</option>
                                <option value="2">Khác</option>
                            </select>
                            <div class="validation-message" id="genderError">Vui lòng chọn giới tính</div>
                        </div>
                        <div class="form-group">
                            <label for="dateOfBirth">Ngày Sinh</label>
                            <input type="date" id="dateOfBirth" name="date_of_birth">
                        </div>
                    </div>

                    <div class="form-row single">
                        <div class="form-group">
                            <label for="currentAddress">Địa Chỉ Hiện Tại</label>
                            <textarea id="currentAddress" name="current_address" placeholder="Nhập địa chỉ hiện tại"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Leader Selection -->
                <div class="form-section">
                    <div class="section-title">Chọn Người Quản Lý</div>
                    <div class="selection-group">
                        <h3>Tìm và chọn người quản lý trực tiếp</h3>
                        <div class="search-input">
                            <input type="text" id="leaderSearch" placeholder="Tìm kiếm theo tên hoặc mã nhân viên...">
                        </div>
                        <div class="options-list" id="leaderList">
                            <!-- Leaders will be populated here -->
                        </div>
                        <div class="selected-info" id="selectedLeaderInfo">
                            <h4>Người quản lý đã chọn:</h4>
                            <p id="leaderName"></p>
                            <p id="leaderPosition"></p>
                        </div>
                        <input type="hidden" id="selectedLeaderId" name="leader_user_id">
                    </div>
                </div>

                <!-- Department Selection -->
                <div class="form-section">
                    <div class="section-title">Chọn Phòng Ban</div>
                    <div class="selection-group">
                        <h3>Tìm và chọn phòng ban</h3>
                        <div class="search-input">
                            <input type="text" id="departmentSearch" placeholder="Tìm kiếm phòng ban...">
                        </div>
                        <div class="options-list" id="departmentList">
                            <!-- Departments will be populated here -->
                        </div>
                        <div class="selected-info" id="selectedDepartmentInfo">
                            <h4>Phòng ban đã chọn:</h4>
                            <p id="departmentName"></p>
                            <p id="departmentDescription"></p>
                        </div>
                        <input type="hidden" id="selectedDepartmentId" name="department_id" required>
                        <div class="validation-message" id="departmentError">Vui lòng chọn phòng ban</div>
                    </div>
                </div>

                <!-- Job Title Selection -->
                <div class="form-section">
                    <div class="section-title">Chọn Chức Danh</div>
                    <div class="selection-group">
                        <h3>Tìm và chọn chức danh công việc</h3>
                        <div class="search-input">
                            <input type="text" id="jobTitleSearch" placeholder="Tìm kiếm chức danh...">
                        </div>
                        <div class="options-list" id="jobTitleList">
                            <!-- Job titles will be populated here -->
                        </div>
                        <div class="selected-info" id="selectedJobTitleInfo">
                            <h4>Chức danh đã chọn:</h4>
                            <p id="jobTitleName"></p>
                            <p id="jobTitleDepartment"></p>
                        </div>
                        <input type="hidden" id="selectedJobTitleId" name="job_title_id" required>
                        <div class="validation-message" id="jobTitleError">Vui lòng chọn chức danh</div>
                    </div>

                    <!-- Job Level Selection (will be populated after job title is selected) -->
                    <div class="selection-group" id="jobLevelSection" style="display: none;">
                        <h3>Chọn Cấp Bậc</h3>
                        <select id="jobLevel" name="job_title_level_id">
                            <option value="">Chọn cấp bậc</option>
                        </select>
                        <input type="hidden" id="selectedJobLevelId">
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="form-section">
                    <div class="section-title">Thông Tin Bổ Sung</div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="personalEmail">Email Cá Nhân</label>
                            <input type="email" id="personalEmail" name="personal_email">
                        </div>
                        <div class="form-group">
                            <label for="homeTown">Quê Quán</label>
                            <input type="text" id="homeTown" name="home_town">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="educationLevel">Trình Độ Học Vấn</label>
                            <select id="educationLevel" name="education_level">
                                <option value="">Chọn trình độ</option>
                                <option value="Tiểu học">Tiểu học</option>
                                <option value="Trung học cơ sở">Trung học cơ sở</option>
                                <option value="Trung học phổ thông">Trung học phổ thông</option>
                                <option value="Trung cấp">Trung cấp</option>
                                <option value="Cao đẳng">Cao đẳng</option>
                                <option value="Đại học">Đại học</option>
                                <option value="Thạc sĩ">Thạc sĩ</option>
                                <option value="Tiến sĩ">Tiến sĩ</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="maritalStatus">Tình Trạng Hôn Nhân</label>
                            <select id="maritalStatus" name="marital_status">
                                <option value="">Chọn tình trạng</option>
                                <option value="0">Độc thân</option>
                                <option value="1">Đã kết hôn</option>
                                <option value="2">Ly hôn</option>
                                <option value="3">Góa</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Đang xử lý thông tin...</p>
                </div>

                <div class="btn-group">
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">Đặt Lại</button>
                    <button type="submit" class="btn btn-primary">Thêm Nhân Viên</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // API Configuration - use relative URL for same-origin requests
        const API_BASE_URL = '/api';
        
        // Data storage
        let appData = {
            leaders: [],
            departments: [],
            jobTitles: []
        };

        // Form state
        let selectedLeader = null;
        let selectedDepartment = null;
        let selectedJobTitle = null;
        let selectedJobLevel = null;

        // Initialize form
        document.addEventListener('DOMContentLoaded', function() {
            loadInitialData();
            setupEventListeners();
        });

        // API functions
        async function apiRequest(endpoint, options = {}) {
            try {
                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error('API request failed:', error);
                throw error;
            }
        }

        async function loadLeaders() {
            try {
                const response = await apiRequest('/leaders');
                return response.data || [];
            } catch (error) {
                console.error('Failed to load leaders:', error);
                return [];
            }
        }

        async function loadDepartments() {
            try {
                const response = await apiRequest('/departments');
                return response.data || [];
            } catch (error) {
                console.error('Failed to load departments:', error);
                return [];
            }
        }

        async function loadJobTitles(departmentId = null) {
            try {
                const endpoint = departmentId ? `/job-titles?department_id=${departmentId}` : '/job-titles';
                const response = await apiRequest(endpoint);
                return response.data || [];
            } catch (error) {
                console.error('Failed to load job titles:', error);
                return [];
            }
        }

        async function loadInitialData() {
            try {
                // Show loading state
                document.querySelectorAll('.options-list').forEach(list => {
                    list.innerHTML = '<div style="padding: 20px; text-align: center;">Đang tải dữ liệu...</div>';
                });

                // Load all data in parallel
                const [leaders, departments, jobTitles] = await Promise.all([
                    loadLeaders(),
                    loadDepartments(),
                    loadJobTitles()
                ]);

                appData.leaders = leaders;
                appData.departments = departments;
                appData.jobTitles = jobTitles;

                // Populate UI
                populateLeaders();
                populateDepartments();
                populateJobTitles();

            } catch (error) {
                console.error('Failed to load initial data:', error);
                showError('Không thể tải dữ liệu. Vui lòng thử lại.');
            }
        }

        function setupEventListeners() {
            // Search functionality
            document.getElementById('leaderSearch').addEventListener('input', filterLeaders);
            document.getElementById('departmentSearch').addEventListener('input', filterDepartments);
            document.getElementById('jobTitleSearch').addEventListener('input', filterJobTitles);

            // Form submission
            document.getElementById('userForm').addEventListener('submit', handleSubmit);

            // Auto-generate full name
            ['firstName', 'middleName', 'lastName'].forEach(id => {
                document.getElementById(id).addEventListener('input', updateFullName);
            });
        }

        function updateFullName() {
            const firstName = document.getElementById('firstName').value.trim();
            const middleName = document.getElementById('middleName').value.trim();
            const lastName = document.getElementById('lastName').value.trim();
            
            const parts = [lastName, middleName, firstName].filter(part => part);
            const fullName = parts.join(' ');
            
            // Update unsigned name for search
            if (fullName) {
                // Simple Vietnamese to ASCII conversion (basic)
                const unsignedName = fullName
                    .toLowerCase()
                    .normalize('NFD')
                    .replace(/[\u0300-\u036f]/g, '')
                    .replace(/đ/g, 'd')
                    .replace(/Đ/g, 'D');
            }
        }

        function populateLeaders() {
            const leaderList = document.getElementById('leaderList');
            leaderList.innerHTML = '';
            
            if (appData.leaders.length === 0) {
                leaderList.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Không có dữ liệu người quản lý</div>';
                return;
            }
            
            appData.leaders.forEach(leader => {
                const jobTitle = leader.employment?.job_title?.name || 'Chưa xác định';
                const department = leader.employment?.job_title?.department_name || 'Chưa xác định';
                const item = createOptionItem(
                    leader,
                    leader.name,
                    `${leader.employee_code} - ${jobTitle} (${department})`,
                    () => selectLeader(leader)
                );
                leaderList.appendChild(item);
            });
        }

        function populateDepartments() {
            const departmentList = document.getElementById('departmentList');
            departmentList.innerHTML = '';
            
            if (appData.departments.length === 0) {
                departmentList.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Không có dữ liệu phòng ban</div>';
                return;
            }
            
            appData.departments.forEach(department => {
                const item = createOptionItem(
                    department,
                    department.name,
                    `${department.department_code || ''} - ${department.description || ''}`,
                    () => selectDepartment(department)
                );
                departmentList.appendChild(item);
            });
        }

        function populateJobTitles() {
            const jobTitleList = document.getElementById('jobTitleList');
            jobTitleList.innerHTML = '';
            
            let jobTitlesToShow = appData.jobTitles;
            
            // Filter by selected department if any
            if (selectedDepartment) {
                jobTitlesToShow = appData.jobTitles.filter(jt => jt.department_id === selectedDepartment.department_id);
            }
            
            if (jobTitlesToShow.length === 0) {
                jobTitleList.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Không có chức danh phù hợp</div>';
                return;
            }
            
            jobTitlesToShow.forEach(jobTitle => {
                const item = createOptionItem(
                    jobTitle,
                    jobTitle.name,
                    `Phòng ban: ${jobTitle.department_name || 'Chưa xác định'}`,
                    () => selectJobTitle(jobTitle)
                );
                jobTitleList.appendChild(item);
            });
        }

        function createOptionItem(data, title, subtitle, onClick) {
            const item = document.createElement('div');
            item.className = 'option-item';
            item.innerHTML = `
                <div>
                    <div><strong>${title}</strong></div>
                    <div class="option-details">${subtitle}</div>
                </div>
            `;
            item.addEventListener('click', onClick);
            return item;
        }

        function selectLeader(leader) {
            selectedLeader = leader;
            document.getElementById('selectedLeaderId').value = leader.user_id;
            
            // Update UI
            document.querySelectorAll('#leaderList .option-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');
            
            // Show selected info
            document.getElementById('leaderName').textContent = leader.name;
            document.getElementById('leaderPosition').textContent = `${leader.employee_code} - ${leader.position} (${leader.department})`;
            document.getElementById('selectedLeaderInfo').classList.add('show');
        }

        async function selectDepartment(department) {
            selectedDepartment = department;
            document.getElementById('selectedDepartmentId').value = department.department_id;
            
            // Update UI
            document.querySelectorAll('#departmentList .option-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');
            
            // Show selected info
            document.getElementById('departmentName').textContent = department.name;
            document.getElementById('departmentDescription').textContent = `${department.department_code || ''} - ${department.description || ''}`;
            document.getElementById('selectedDepartmentInfo').classList.add('show');
            
            // Load job titles for this department
            try {
                const jobTitles = await loadJobTitles(department.department_id);
                appData.jobTitles = jobTitles;
                populateJobTitles();
            } catch (error) {
                console.error('Failed to load job titles for department:', error);
                showError('Không thể tải danh sách chức danh cho phòng ban này');
            }
            
            // Clear validation error
            document.getElementById('departmentError').classList.remove('show');
        }

        function selectJobTitle(jobTitle) {
            selectedJobTitle = jobTitle;
            document.getElementById('selectedJobTitleId').value = jobTitle.job_title_id;
            
            // Update UI
            document.querySelectorAll('#jobTitleList .option-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');
            
            // Show selected info
            document.getElementById('jobTitleName').textContent = jobTitle.name;
            document.getElementById('jobTitleDepartment').textContent = `Phòng ban: ${jobTitle.department_name || 'Chưa xác định'}`;
            document.getElementById('selectedJobTitleInfo').classList.add('show');
            
            // Populate job levels
            populateJobLevels(jobTitle.levels || []);
            
            // Clear validation error
            document.getElementById('jobTitleError').classList.remove('show');
        }

        function populateJobLevels(levels) {
            const jobLevelSelect = document.getElementById('jobLevel');
            const jobLevelSection = document.getElementById('jobLevelSection');
            
            jobLevelSelect.innerHTML = '<option value="">Chọn cấp bậc</option>';
            
            levels.forEach(level => {
                const option = document.createElement('option');
                option.value = level.job_title_level_id;
                option.textContent = `${level.name} (Cấp ${level.level})`;
                jobLevelSelect.appendChild(option);
            });
            
            // Show job level section
            jobLevelSection.style.display = 'block';
            
            // Auto-select if only one level
            if (levels.length === 1) {
                jobLevelSelect.value = levels[0].job_title_level_id;
                selectedJobLevel = levels[0];
            }
        }

        function showError(message) {
            alert(message); // Simple error display - could be improved with a toast/modal
        }

        function filterLeaders() {
            const searchTerm = document.getElementById('leaderSearch').value.toLowerCase();
            filterOptionList('leaderList', appData.leaders, searchTerm, (leader, term) => {
                const jobTitle = leader.employment?.job_title?.name || '';
                return leader.name.toLowerCase().includes(term) || 
                       leader.employee_code.toLowerCase().includes(term) ||
                       jobTitle.toLowerCase().includes(term);
            });
        }

        function filterDepartments() {
            const searchTerm = document.getElementById('departmentSearch').value.toLowerCase();
            filterOptionList('departmentList', appData.departments, searchTerm, (dept, term) => {
                return dept.name.toLowerCase().includes(term) || 
                       (dept.department_code && dept.department_code.toLowerCase().includes(term)) ||
                       (dept.description && dept.description.toLowerCase().includes(term));
            });
        }

        function filterJobTitles() {
            const searchTerm = document.getElementById('jobTitleSearch').value.toLowerCase();
            let jobTitles = appData.jobTitles;
            
            // Filter by selected department if any
            if (selectedDepartment) {
                jobTitles = appData.jobTitles.filter(jt => jt.department_id === selectedDepartment.department_id);
            }
                
            filterOptionList('jobTitleList', jobTitles, searchTerm, (jobTitle, term) => {
                return jobTitle.name.toLowerCase().includes(term) || 
                       (jobTitle.department_name && jobTitle.department_name.toLowerCase().includes(term));
            });
        }

        function filterOptionList(listId, data, searchTerm, matchFunction) {
            const list = document.getElementById(listId);
            const items = list.querySelectorAll('.option-item');
            
            items.forEach((item, index) => {
                if (matchFunction(data[index], searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function validateForm() {
            let isValid = true;
            
            // Clear previous errors
            document.querySelectorAll('.validation-message').forEach(msg => {
                msg.classList.remove('show');
            });
            
            // Required field validation
            const requiredFields = [
                { id: 'firstName', errorId: 'firstNameError', message: 'Vui lòng nhập tên' },
                { id: 'lastName', errorId: 'lastNameError', message: 'Vui lòng nhập họ' },
                { id: 'primaryEmail', errorId: 'emailError', message: 'Vui lòng nhập email hợp lệ' },
                { id: 'phoneNumber', errorId: 'phoneError', message: 'Vui lòng nhập số điện thoại' },
                { id: 'gender', errorId: 'genderError', message: 'Vui lòng chọn giới tính' }
            ];
            
            requiredFields.forEach(field => {
                const value = document.getElementById(field.id).value.trim();
                if (!value) {
                    document.getElementById(field.errorId).classList.add('show');
                    isValid = false;
                }
            });
            
            // Email validation
            const email = document.getElementById('primaryEmail').value.trim();
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (email && !emailPattern.test(email)) {
                document.getElementById('emailError').textContent = 'Email không hợp lệ';
                document.getElementById('emailError').classList.add('show');
                isValid = false;
            }
            
            // Department validation
            if (!selectedDepartment) {
                document.getElementById('departmentError').classList.add('show');
                isValid = false;
            }
            
            // Job title validation
            if (!selectedJobTitle) {
                document.getElementById('jobTitleError').classList.add('show');
                isValid = false;
            }
            
            return isValid;
        }

        function generateApiPayload() {
            const userEntryType = document.querySelector('input[name="user_entry_type"]:checked').value;
            
            // Build API payload according to UserCreateRequest model
            const payload = {
                // Basic Information
                first_name: document.getElementById('firstName').value.trim(),
                middle_name: document.getElementById('middleName').value.trim() || null,
                last_name: document.getElementById('lastName').value.trim(),
                employee_code: document.getElementById('employeeCode').value.trim() || null,
                primary_email: document.getElementById('primaryEmail').value.trim(),
                phone_number: document.getElementById('phoneNumber').value.trim(),
                gender: parseInt(document.getElementById('gender').value),
                date_of_birth: document.getElementById('dateOfBirth').value || null,
                current_address: document.getElementById('currentAddress').value.trim() || null,
                
                // Additional Information
                personal_email: document.getElementById('personalEmail').value.trim() || null,
                home_town: document.getElementById('homeTown').value.trim() || null,
                education_level: document.getElementById('educationLevel').value || null,
                marital_status: document.getElementById('maritalStatus').value ? parseInt(document.getElementById('maritalStatus').value) : null,
                
                // Work-related Information
                leader_user_id: selectedLeader ? selectedLeader.user_id : null,
                department_id: selectedDepartment.department_id,
                job_title_id: selectedJobTitle.job_title_id,
                job_title_level_id: selectedJobLevel ? selectedJobLevel.job_title_level_id : null,
                
                // User Entry Type
                user_entry_type: userEntryType,
                
                // Company context
                company_id: 'comp_001' // This would come from session/context in real app
            };
            
            return payload;
        }

        async function handleSubmit(event) {
            event.preventDefault();
            
            if (!validateForm()) {
                return;
            }
            
            // Show loading
            document.getElementById('loading').classList.add('show');
            document.querySelector('.btn-primary').disabled = true;
            
            try {
                const payload = generateApiPayload();
                
                // Submit to API
                const response = await apiRequest('/users', {
                    method: 'POST',
                    body: JSON.stringify(payload)
                });
                
                if (response.success) {
                    // Show success message with details
                    const successMsg = document.getElementById('successMessage');
                    successMsg.innerHTML = `
                        <strong>Thành công!</strong> Nhân viên đã được thêm vào hệ thống.<br>
                        <small>Mã nhân viên: ${response.employee_code} | ID: ${response.user_id}</small>
                    `;
                    successMsg.classList.add('show');
                    
                    // Reset form after delay
                    setTimeout(() => {
                        resetForm();
                        successMsg.classList.remove('show');
                    }, 5000);
                } else {
                    throw new Error(response.message || 'Không thể tạo nhân viên');
                }
                
            } catch (error) {
                console.error('Submit error:', error);
                let errorMessage = 'Có lỗi xảy ra khi tạo nhân viên';
                
                if (error.message.includes('Email address already exists')) {
                    errorMessage = 'Địa chỉ email đã tồn tại trong hệ thống';
                } else if (error.message.includes('Employee code already exists')) {
                    errorMessage = 'Mã nhân viên đã tồn tại trong hệ thống';
                } else if (error.message.includes('Department not found')) {
                    errorMessage = 'Phòng ban không tồn tại';
                } else if (error.message.includes('Job title not found')) {
                    errorMessage = 'Chức danh không tồn tại';
                } else if (error.message) {
                    errorMessage = error.message;
                }
                
                showError(errorMessage);
            } finally {
                // Hide loading
                document.getElementById('loading').classList.remove('show');
                document.querySelector('.btn-primary').disabled = false;
            }
        }

        function resetForm() {
            // Reset form fields
            document.getElementById('userForm').reset();
            
            // Reset selections
            selectedLeader = null;
            selectedDepartment = null;
            selectedJobTitle = null;
            selectedJobLevel = null;
            
            // Reset UI
            document.querySelectorAll('.selected-info').forEach(info => {
                info.classList.remove('show');
            });
            
            document.querySelectorAll('.option-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            document.querySelectorAll('.validation-message').forEach(msg => {
                msg.classList.remove('show');
            });
            
            // Hide job level section
            document.getElementById('jobLevelSection').style.display = 'none';
            
            // Reset job title list to show all
            populateJobTitles();
            
            // Reset to manual user type
            document.getElementById('manualUser').checked = true;
            
            // Reload data for next entry
            loadInitialData();
        }
    </script>
</body>
</html>