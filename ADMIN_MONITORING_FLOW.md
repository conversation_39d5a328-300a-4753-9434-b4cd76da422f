# Admin Monitoring Architecture Flow

## Proper Layered Architecture Implementation

Following the README.md documented architecture pattern, the admin monitoring system now correctly implements the layered flow:

```
Middleware → Controller → Repository → Model → Database
    ↓          ↓           ↓            ↓       ↓
 Request   Business    Data Access   Entity  Storage
 Handler    Logic       Layer        Layer   Layer
```

## Implementation Details

### 1. **Middleware Layer** (`src/middleware/authorization.py`)
- **Role**: Request interception and initial processing
- **Responsibility**: 
  - Intercept HTTP requests
  - Extract user information
  - Delegate logging to controller
- **Flow**: `AuthorizationMiddleware` → `AdminMonitoringController`

```python
# Middleware calls controller (NOT model directly)
await self.monitoring_controller.log_user_access(
    username=username,
    path=request.url.path,
    method=request.method,
    user_roles=roles,
    user_id=str(user_id),
    ip_address=client_ip,
    user_agent=user_agent,
    additional_data=additional_data
)
```

### 2. **Controller Layer** (`src/controllers/admin_monitoring_controller.py`)
- **Role**: Business logic processing
- **Responsibility**:
  - Process business rules for logging
  - Coordinate between multiple repositories if needed
  - Handle complex business workflows
- **Flow**: `AdminMonitoringController` → `LogUserRepository`

```python
# Controller uses repository (NOT model directly)
return await self.log_repository.log_admin_access(
    username=username,
    path=path,
    method=method,
    user_roles=user_roles,
    user_id=user_id,
    ip_address=ip_address,
    user_agent=user_agent,
    additional_data=additional_data
)
```

### 3. **Repository Layer** (`src/repositories/log_user_repository.py`)
- **Role**: Data access abstraction
- **Responsibility**:
  - Abstract database operations
  - Handle data mapping and transformation
  - Manage database connections
- **Flow**: `LogUserRepository` → `LogUserModel`

```python
# Repository calls model
return await self.log_model.log_admin_access(
    username=username,
    path=path,
    method=method,
    user_roles=user_roles,
    user_id=user_id,
    ip_address=ip_address,
    user_agent=user_agent,
    additional_data=additional_data
)
```

### 4. **Model Layer** (`src/models/mongodb/log_user_model.py`)
- **Role**: Data entity and database schema
- **Responsibility**:
  - Define database collections/tables
  - Handle direct database operations
  - Implement entity-specific business logic
- **Flow**: `LogUserModel` → MongoDB Database

```python
# Model performs actual database operations
result = await self.insert(log_data)
return str(result.inserted_id)
```

## Benefits of This Architecture

### ✅ **Separation of Concerns**
- Each layer has a single responsibility
- Middleware handles request interception
- Controller manages business logic
- Repository abstracts data access
- Model defines data structure

### ✅ **Maintainability**
- Easy to modify any layer without affecting others
- Clear boundaries between components
- Follows established patterns

### ✅ **Testability**
- Each layer can be tested independently
- Mock repositories for controller testing
- Mock models for repository testing

### ✅ **Scalability**
- Can replace MongoDB with other databases by changing only repository
- Can add caching layer in repository
- Can modify business logic without touching middleware

### ✅ **Reusability**
- Repository can be used by multiple controllers
- Model can be accessed through different repositories
- Controller methods can be called from different middlewares

## API Endpoints Available

- `GET /api/v1.0/admin/access-logs` - Get admin access logs (Admin only)
- `GET /api/v1.0/admin/user-activity-stats` - Get user activity statistics (Admin only)  
- `GET /api/v1.0/user/my-activity` - Get current user's activity logs

## Data Flow Example

1. **HTTP Request** comes to any API endpoint
2. **AuthorizationMiddleware** intercepts the request
3. **Middleware** calls `AdminMonitoringController.log_user_access()`
4. **Controller** processes business logic and calls `LogUserRepository.log_admin_access()`
5. **Repository** handles data access and calls `LogUserModel.log_admin_access()`
6. **Model** performs actual MongoDB insertion
7. **Response** flows back through the layers

This ensures proper separation of concerns and follows the documented architecture pattern.