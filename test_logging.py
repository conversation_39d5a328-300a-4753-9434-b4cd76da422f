#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 16/06/2025
"""

import asyncio

from src.models.mongo.evaluate_period_model import EvaluatePeriodModel


async def get_data_evaluate_period():
    """
    Get data evaluate period
    """

    # x2 = await EvaluatePeriodModel().update_by_set(
    #     {"specific_department_ids": {"$exists": False}}, {"specific_department_ids": []}, upsert=True
    # )
    # print("x2:: ", x2)
    x1 = await EvaluatePeriodModel().delete_({})
    print("x1:: ", x1)
    for x in x1:
        print("x:: ", x)

    #     x2 = await EvaluatePeriodModel().update_by_set(
    #         {"_id": x["_id"]}, {"specific_department_ids": []}
    #     )
    # print("x2:: ", x2)
    # print("x1:: ", x1)
    # start_time = x1["start_time"]
    # end_time = x1["end_time"]

    # start_time_update = start_time.replace(day=1, month=7)
    # end_time_update = end_time.replace(day=18, month=7)

    # await EvaluatePeriodModel().update_one_query(
    #     {"_id": ObjectId("678476fa1ff286fe77af5e70")},
    #     {"start_time": start_time_update, "end_time": end_time_update},
    # )

    # await EvaluateModel().update_one_query(
    #     {"evaluate_period_id": ObjectId("678476fa1ff286fe77af5e70"), "user_id": "3cdf67c1-370e-4861-bc3b-4bd48a345f72"},
    #     {"evaluate_period_id": ObjectId("66fcf3173f658940a0835050")},
    # )
    # await EvaluateModel().delete_one(
    #     {"evaluate_period_id": ObjectId("678476fa1ff286fe77af5e70"), "user_id": "673292d8-6754-44ac-a3b6-86803e4c8034"},
    # )


# async def update_job_title_level_test_manual():
#     session_db: Session = next(get_db())
#     users = (
#         session_db.query(UserModel)
#         .filter(
#             UserModel.user_id.in_(
#                 [
#                     "3ce48706-c876-429a-87e5-803772ce40f6",
#                     "79bbda14-b351-4b4c-abbc-1907ecfbf5b5",
#                     "73023a8b-5771-4eb5-b913-9f6f72e2f40b",
#                     "673292d8-6754-44ac-a3b6-86803e4c8034",
#                     "f134ddf6-ae1c-4459-bbaa-0b5ec6c7488a",
#                     "df07379d-bf6c-4171-88e1-ec5969021846",
#                     "46153623-815b-43b4-89d2-0adce8ffb674",
#                     "8496b4c5-c9bb-491c-9106-3b81cdb5a1e9",
#                     "a115f438-79ce-4ce0-9de2-6385d5ae42bf",
#                     "f6a2c46d-d96b-458e-b94c-0bac7d0ad639",
#                 ]
#             )
#         )
#         .all()
#     )

#     for user in users:
#         user.job_title_level_id = ""
#     session_db.commit()


# async def get_role_user():
#     session_db: Session = next(get_db())
#     users = (
#         session_db.query(UserModel)
#         .filter(
#             UserModel.user_id.in_(
#                 [
#                     "3ce48706-c876-429a-87e5-803772ce40f6",
#                     "79bbda14-b351-4b4c-abbc-1907ecfbf5b5",
#                     "73023a8b-5771-4eb5-b913-9f6f72e2f40b",
#                     "673292d8-6754-44ac-a3b6-86803e4c8034",
#                     "f134ddf6-ae1c-4459-bbaa-0b5ec6c7488a",
#                     "df07379d-bf6c-4171-88e1-ec5969021846",
#                     "46153623-815b-43b4-89d2-0adce8ffb674",
#                     "8496b4c5-c9bb-491c-9106-3b81cdb5a1e9",
#                     "a115f438-79ce-4ce0-9de2-6385d5ae42bf",
#                     "f6a2c46d-d96b-458e-b94c-0bac7d0ad639",
#                 ]
#             )
#         )
#         .all()
#     )
#     for user in users:
#         if user.user_id == "673292d8-6754-44ac-a3b6-86803e4c8034":
#             user.leader_user_id = "df07379d-bf6c-4171-88e1-ec5969021846"
#     session_db.commit()


# async def get_department_ids_by_permission_user(roles, user_department_repo, account_id):
#     department_ids = []
#     if RoleChoice.ADMIN.value in roles:
#         return []
#     if RoleChoice.LEADER.value in roles or RoleChoice.USER.value in roles:
#         user_departments = await user_department_repo.get_user_departments_by_user_id(account_id)
#         for user_department in user_departments:
#             department_ids.append(user_department.department_id)
#     return department_ids


# async def get_period_evaluate():
#     session_db: Session = next(get_db())
#     company = session_db.query(CompanyModel).first()
#     company_id = company.company_id
#     roles = [RoleChoice.LEADER.value, RoleChoice.USER.value]
#     user_department_repo = UserDepartmentRepository(session_db)
#     account_id = "673292d8-6754-44ac-a3b6-86803e4c8034"
#     department_ids = await get_department_ids_by_permission_user(roles, user_department_repo, account_id)

#     # filter_type_default = request.query_params.get(QueryParamChoice.FILTER_TYPE_DEFAULT, "")

#     filter_type_default = None

#     filter_type_default = datetime.datetime.now(datetime.timezone.utc)

#     evaluate_period_model: EvaluatePeriodModel = EvaluatePeriodModel()
#     results = await evaluate_period_model.get_evaluate_periods(
#         company_id, [], None, None, None, None, department_ids, filter_type_default
#     )
#     print("results:: ", results)
#     result_evaluate_periods = await EvaluatePeriodModel().find({})
#     for result_evaluate_period in result_evaluate_periods:
#         print("result_evaluate_period:: ", result_evaluate_period)


# async def get_company_id():
#     session_db: Session = next(get_db())
#     company = session_db.query(CompanyModel).first()
#     print(company.company_id)
#     return


# def _convert_interval_default_time_to_current_time(default_start_time, default_end_time):
#     current_year = datetime.datetime.now(datetime.UTC).year
#     # Replace current year
#     current_start_time = default_start_time.replace(year=current_year)
#     current_end_time = default_end_time.replace(year=current_year)
#     # Check extend to next year
#     if current_end_time <= current_start_time:
#         current_end_time = current_end_time.replace(year=current_end_time.year + 1)
#     return current_start_time, current_end_time


# async def ensure_default_evaluate_period(list_evaluate_period_template):
#     log_prefix = "ensure_default_evaluate_period"
#     time_now = datetime.datetime.now(datetime.UTC)
#     MobioLogging().info(f"{log_prefix}::start::time_now::{str(time_now)}")
#     session_db: Session = next(get_db())
#     created_by = "admin"
#     evaluate_period_model = EvaluatePeriodModel()
#     company_repo: CompanyRepository = CompanyRepository(session_db)

#     # ================================================== Init evaluate period ================================================== #
#     for evaluate_period_data in list_evaluate_period_template:

#         # Get actual time
#         actual_evaluate_period_start_time, actual_evaluate_period_end_time = (
#             _convert_interval_default_time_to_current_time(
#                 evaluate_period_data[EvaluatePeriodKey.START_TIME], evaluate_period_data[EvaluatePeriodKey.END_TIME]
#             )
#         )

#         actual_evaluate_period_start_time_month = actual_evaluate_period_start_time.month
#         month_review = None
#         if 1 <= actual_evaluate_period_start_time_month <= 6:
#             month_review = EvaluatePeriodEnum.Type.NUMBER_MONTH_SIX
#         elif 7 <= actual_evaluate_period_start_time_month <= 12:
#             month_review = EvaluatePeriodEnum.Type.NUMBER_MONTH_TWELVE

#         # Name:
#         name = evaluate_period_data[EvaluatePeriodKey.NAME]
#         if name:
#             name += f" tháng {actual_evaluate_period_start_time.month}/{actual_evaluate_period_start_time.year}"
#         else:
#             name = f"Tháng {actual_evaluate_period_start_time.month}/{actual_evaluate_period_start_time.year}"

#         name = name.strip()
#         MobioLogging().info(f"{log_prefix}::name::{name}")

#         list_company = await company_repo.get_all_company(company_ids=[])

#         MobioLogging().info(f"{log_prefix}::list_company::{list_company}")

#         # Start time aggregate performance
#         start_time_aggregate_performance = None
#         if evaluate_period_data.get(EvaluatePeriodKey.START_TIME_AGGREGATE_PERFORMANCE):
#             start_time_aggregate_performance = evaluate_period_data.get(
#                 EvaluatePeriodKey.START_TIME_AGGREGATE_PERFORMANCE
#             )

#         # End time aggregate performance
#         end_time_aggregate_performance = None
#         if evaluate_period_data.get(EvaluatePeriodKey.END_TIME_AGGREGATE_PERFORMANCE):
#             end_time_aggregate_performance = evaluate_period_data.get(EvaluatePeriodKey.END_TIME_AGGREGATE_PERFORMANCE)

#         if not start_time_aggregate_performance or not end_time_aggregate_performance:
#             if month_review == EvaluatePeriodEnum.Type.NUMBER_MONTH_SIX:
#                 start_time_aggregate_performance = actual_evaluate_period_start_time.replace(
#                     month=1, day=1, hour=0, minute=0, second=0
#                 )
#                 end_time_aggregate_performance = actual_evaluate_period_start_time.replace(
#                     month=6, day=30, hour=23, minute=59, second=59
#                 )
#             elif month_review == EvaluatePeriodEnum.Type.NUMBER_MONTH_TWELVE:
#                 start_time_aggregate_performance = actual_evaluate_period_start_time.replace(
#                     month=7, day=1, hour=0, minute=0, second=0
#                 )
#                 end_time_aggregate_performance = actual_evaluate_period_start_time.replace(
#                     month=12, day=31, hour=23, minute=59, second=59
#                 )

#         for company in list_company:
#             MobioLogging().info(f"{log_prefix}::company::{company.name}")
#             MobioLogging().info(f"{log_prefix}::actual_evaluate_period_start_time::{actual_evaluate_period_start_time}")
#             # Fetch data
#             evaluate_period = await evaluate_period_model.get_evaluate_period_by_start_time(
#                 company.company_id, actual_evaluate_period_start_time
#             )
#             MobioLogging().info(f"{log_prefix}::evaluate_period::{evaluate_period}")
#             # Check existed evaluate period
#             if evaluate_period:
#                 evaluate_period_id = evaluate_period["_id"]
#                 MobioLogging().debug(f"{log_prefix}::evaluate_period::{evaluate_period}::already_exists")
#                 # Update evaluate period
#                 data_update = {}
#                 if evaluate_period.get("start_time") != actual_evaluate_period_start_time:
#                     data_update[EvaluatePeriodKey.START_TIME] = actual_evaluate_period_start_time
#                 if evaluate_period.get("end_time") != actual_evaluate_period_end_time:
#                     data_update[EvaluatePeriodKey.END_TIME] = actual_evaluate_period_end_time
#                 if evaluate_period.get("name") != name:
#                     data_update[EvaluatePeriodKey.NAME] = name
#                 if evaluate_period.get("config") != evaluate_period_data[EvaluatePeriodKey.CONFIG]:
#                     data_update[EvaluatePeriodKey.CONFIG] = evaluate_period_data[EvaluatePeriodKey.CONFIG]
#                 if evaluate_period.get("exclude_department_ids", []) != evaluate_period_data.get(
#                     EvaluatePeriodKey.EXCLUDE_DEPARTMENT_IDS, []
#                 ):
#                     data_update[EvaluatePeriodKey.EXCLUDE_DEPARTMENT_IDS] = evaluate_period_data.get(
#                         EvaluatePeriodKey.EXCLUDE_DEPARTMENT_IDS, []
#                     )
#                 if evaluate_period.get("specific_department_ids", []) != evaluate_period_data.get(
#                     EvaluatePeriodKey.SPECIFIC_DEPARTMENT_IDS, []
#                 ):
#                     data_update[EvaluatePeriodKey.SPECIFIC_DEPARTMENT_IDS] = evaluate_period_data.get(
#                         EvaluatePeriodKey.SPECIFIC_DEPARTMENT_IDS, []
#                     )
#                 if data_update:
#                     data_update.update(
#                         {
#                             CommonKey.UPDATED_TIME: time_now,
#                             CommonKey.UPDATED_BY: created_by,
#                         }
#                     )
#                     await evaluate_period_model.update_one_query(
#                         {
#                             CommonKey.ID: evaluate_period_id,
#                         },
#                         data_update,
#                     )
#                     MobioLogging().debug(f"{log_prefix}::{name}::data_update::{data_update}::updated")
#                 else:
#                     MobioLogging().debug(f"{log_prefix}::{name}::not_updated")
#             else:
#                 # Create new
#                 evaluate_period_data_insert = {
#                     EvaluatePeriodKey.START_TIME: actual_evaluate_period_start_time,
#                     EvaluatePeriodKey.END_TIME: actual_evaluate_period_end_time,
#                     EvaluatePeriodKey.REPEAT_TYPE: evaluate_period_data[EvaluatePeriodKey.REPEAT_TYPE],
#                     EvaluatePeriodKey.CONFIG: evaluate_period_data.get(EvaluatePeriodKey.CONFIG, {}),
#                     EvaluatePeriodKey.EXCLUDE_DEPARTMENT_IDS: evaluate_period_data.get(
#                         EvaluatePeriodKey.EXCLUDE_DEPARTMENT_IDS, []
#                     ),
#                     EvaluatePeriodKey.SPECIFIC_DEPARTMENT_IDS: evaluate_period_data.get(
#                         EvaluatePeriodKey.SPECIFIC_DEPARTMENT_IDS, []
#                     ),
#                     EvaluatePeriodKey.NAME: name,
#                     EvaluatePeriodKey.START_TIME_AGGREGATE_PERFORMANCE: start_time_aggregate_performance,
#                     EvaluatePeriodKey.END_TIME_AGGREGATE_PERFORMANCE: end_time_aggregate_performance,
#                     CommonKey.COMPANY_ID: company.company_id,
#                     CommonKey.CREATED_TIME: time_now,
#                     CommonKey.UPDATED_TIME: time_now,
#                     CommonKey.CREATED_BY: created_by,
#                     CommonKey.UPDATED_BY: created_by,
#                     EvaluatePeriodKey.STATUS: 1,
#                     EvaluatePeriodKey.MONTH_REVIEW: month_review,
#                 }
#                 inserted_result = await evaluate_period_model.insert_evaluate_period(evaluate_period_data_insert)
#                 MobioLogging().debug(
#                     f"{log_prefix}::evaluate_period::{evaluate_period_data}::created::{inserted_result}"
#                 )

#     MobioLogging().info(f"{log_prefix}::finished")


# async def add_evaluate_period_template():
#     from src.cronjobs.handler_system_auto_generate_evaluate_cronjob import (
#         HandlerAutoGenerateEvaluate,
#     )

#     company_id = await get_company_id()

#     template = {
#         "created_by": "df07379d-bf6c-4171-88e1-ec5969021846",
#         "updated_by": "df07379d-bf6c-4171-88e1-ec5969021846",
#         "name": "Kỳ đánh giá tháng 6/2025",
#         "start_time": datetime.datetime.strptime("2025-06-15T17:01:00Z", "%Y-%m-%dT%H:%M:%SZ"),
#         "end_time": datetime.datetime.strptime("2025-06-30T16:59:59Z", "%Y-%m-%dT%H:%M:%SZ"),
#         "config": {"employee_evaluate_interval_day": 4, "leader_evaluate_interval_day": 11},
#         "specific_department_ids": ["d499d7b9-4d6e-46eb-aedc-ca7614a88025"],
#         "repeat_type": EvaluatePeriodEnum.RepeatType.ONCE,
#         "company_id": company_id,
#     }

#     await ensure_default_evaluate_period([template])
#     await HandlerAutoGenerateEvaluate().async_owner_do()


if __name__ == "__main__":
    asyncio.run(get_data_evaluate_period())
