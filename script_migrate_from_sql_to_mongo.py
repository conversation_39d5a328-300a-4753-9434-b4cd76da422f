#!/usr/bin/env python3
"""
Migration Script: PostgreSQL to MongoDB
Migrates HR management system data from PostgreSQL to MongoDB with optimized schema.
Based on sql.sql and mongodb.schema specifications.
"""

import asyncio
import logging
import sys
from datetime import datetime
from typing import Any, Dict

import asyncpg
from motor.motor_asyncio import AsyncIOMotorClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("migration.log"), logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger(__name__)


class SQLToMongoMigrator:
    """Main migration class handling PostgreSQL to MongoDB data transfer."""

    def __init__(self, postgres_url: str, mongo_url: str, mongo_db_name: str):
        self.postgres_url = postgres_url
        self.mongo_url = mongo_url
        self.mongo_db_name = mongo_db_name
        self.pg_conn = None
        self.mongo_client = None
        self.mongo_db = None
        self.migration_stats = {}

    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()

    async def connect(self):
        """Establish connections to both databases."""
        try:
            # Connect to PostgreSQL
            self.pg_conn = await asyncpg.connect(self.postgres_url)
            logger.info("Connected to PostgreSQL")

            # Connect to MongoDB
            self.mongo_client = AsyncIOMotorClient(self.mongo_url)
            self.mongo_db = self.mongo_client[self.mongo_db_name]
            logger.info("Connected to MongoDB")

        except Exception as e:
            logger.error(f"Connection failed: {e}")
            raise

    async def disconnect(self):
        """Close database connections."""
        if self.pg_conn:
            await self.pg_conn.close()
            logger.info("PostgreSQL connection closed")

        if self.mongo_client:
            self.mongo_client.close()
            logger.info("MongoDB connection closed")

    async def migrate_companies(self) -> Dict[str, Any]:
        """Migrate companies table."""
        logger.info("Starting companies migration...")

        query = """
        SELECT company_id, tenant_key, name, avatar, status, 
               created_by, updated_by, created_time, updated_time
        FROM company
        ORDER BY created_time
        """

        rows = await self.pg_conn.fetch(query)
        companies = []

        for row in rows:
            company_doc = {
                "company_id": row["company_id"],
                "tenant_key": row["tenant_key"],
                "name": row["name"],
                "avatar": row["avatar"],
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            companies.append(company_doc)

        if companies:
            await self.mongo_db.companies.insert_many(companies)
            logger.info(f"Migrated {len(companies)} companies")
            print('=================CHECK COMPANIES ==============================\n')
            print(companies)
            print('=================CHECK COMPANIES ==============================\n')

        return {"migrated": len(companies), "collection": "companies"}

    async def migrate_employment_types(self) -> Dict[str, Any]:
        """Migrate employment_type table."""
        logger.info("Starting employment_types migration...")

        query = """
        SELECT employment_type_id, company_id, vi_name, en_name, description,
               status, created_by, updated_by, created_time, updated_time
        FROM employment_type
        ORDER BY employment_type_id
        """

        rows = await self.pg_conn.fetch(query)
        employment_types = []

        for row in rows:
            doc = {
                "employment_type_id": row["employment_type_id"],
                "company_id": row["company_id"],
                "vi_name": row["vi_name"],
                "en_name": row["en_name"],
                "description": row["description"],
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            employment_types.append(doc)

        if employment_types:
            await self.mongo_db.employment_types.insert_many(employment_types)
            logger.info(f"Migrated {len(employment_types)} employment types")

        return {"migrated": len(employment_types), "collection": "employment_types"}

    async def migrate_contracts(self) -> Dict[str, Any]:
        """Migrate contract table."""
        logger.info("Starting contracts migration...")

        query = """
        SELECT contract_id, company_id, contract_number, contract_file_scan_link,
               status, created_by, updated_by, created_time, updated_time
        FROM contract
        ORDER BY contract_id
        """

        rows = await self.pg_conn.fetch(query)
        contracts = []

        for row in rows:
            doc = {
                "contract_id": row["contract_id"],
                "company_id": row["company_id"],
                "contract_number": row["contract_number"],
                "contract_file_scan_link": row["contract_file_scan_link"],
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            contracts.append(doc)

        if contracts:
            await self.mongo_db.contracts.insert_many(contracts)
            logger.info(f"Migrated {len(contracts)} contracts")

        return {"migrated": len(contracts), "collection": "contracts"}

    async def migrate_branches(self) -> Dict[str, Any]:
        """Migrate branch table."""
        logger.info("Starting branches migration...")

        query = """
        SELECT branch_id, company_id, code, name, address, phone, owner_code,
               status, created_by, updated_by, created_time, updated_time
        FROM branch
        ORDER BY created_time
        """

        rows = await self.pg_conn.fetch(query)
        branches = []

        for row in rows:
            doc = {
                "branch_id": row["branch_id"],
                "company_id": row["company_id"],
                "code": row["code"],
                "name": row["name"],
                "address": row["address"],
                "phone": row["phone"],
                "owner_code": row["owner_code"],
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            branches.append(doc)

        if branches:
            await self.mongo_db.branches.insert_many(branches)
            logger.info(f"Migrated {len(branches)} branches")

        return {"migrated": len(branches), "collection": "branches"}

    async def migrate_policies(self) -> Dict[str, Any]:
        """Migrate policies table."""
        logger.info("Starting policies migration...")

        query = """
        SELECT policy_id, name, description, policy_type, company_id, document,
               created_by, status, updated_by, created_time, updated_time
        FROM policies
        ORDER BY created_time
        """

        rows = await self.pg_conn.fetch(query)
        policies = []

        for row in rows:
            doc = {
                "policy_id": row["policy_id"],
                "name": row["name"],
                "description": row["description"],
                "policy_type": row["policy_type"],
                "company_id": row["company_id"],
                "document": row["document"],
                "created_by": row["created_by"],
                "status": row["status"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            policies.append(doc)

        if policies:
            await self.mongo_db.policies.insert_many(policies)
            logger.info(f"Migrated {len(policies)} policies")

        return {"migrated": len(policies), "collection": "policies"}

    async def migrate_departments(self) -> Dict[str, Any]:
        """Migrate departments with optimized schema and embedded sub_departments."""
        logger.info("Starting departments migration...")

        # Get main departments
        dept_query = """
        SELECT department_id, company_id, department_code, lark_department_id, 
               open_department_id, display, name, lower_case_name, description, 
               "order", owners, status, created_by, updated_by, created_time, updated_time
        FROM department
        ORDER BY created_time
        """

        # Get sub departments
        sub_dept_query = """
        SELECT sub_department_id, company_id, code, name, department_id, description,
               status, created_by, updated_by, created_time, updated_time
        FROM sub_department
        ORDER BY created_time
        """

        dept_rows = await self.pg_conn.fetch(dept_query)
        sub_dept_rows = await self.pg_conn.fetch(sub_dept_query)

        # Group sub departments by parent department
        sub_depts_by_parent = {}
        for sub_dept in sub_dept_rows:
            parent_id = sub_dept["department_id"]
            if parent_id not in sub_depts_by_parent:
                sub_depts_by_parent[parent_id] = []

            sub_depts_by_parent[parent_id].append(
                {
                    "sub_department_id": sub_dept["sub_department_id"],
                    "code": sub_dept["code"],
                    "name": sub_dept["name"],
                    "description": sub_dept["description"],
                    "order": 0,  # Default order
                    "manager_ids": [],  # To be populated later
                    "employee_count": 0,  # To be computed
                    "status": sub_dept["status"],
                    "created_by": sub_dept["created_by"],
                    "updated_by": sub_dept["updated_by"],
                    "created_time": sub_dept["created_time"],
                    "updated_time": sub_dept["updated_time"],
                }
            )

        departments = []
        for row in dept_rows:
            # Convert PostgreSQL array to Python list
            owners = row["owners"] if row["owners"] else []

            dept_doc = {
                "department_id": row["department_id"],
                "company_id": row["company_id"],
                # Core Information (Optimized for Search)
                "identity": {
                    "code": row["department_code"],
                    "name": row["name"],
                    "lower_case_name": row["lower_case_name"],
                    "description": row["description"],
                    "display": row["display"] or 1,
                },
                # External System Integration
                "external_ids": {
                    "lark_department_id": row["lark_department_id"],
                    "open_department_id": row["open_department_id"],
                },
                # Hierarchy and Management
                "hierarchy": {
                    "order": row["order"] or 0,
                    "owners": owners,
                    "parent_department_id": None,  # To be computed later
                    "level": 1,  # Default level
                    "path": f"/{row['name'].lower().replace(' ', '_')}",
                },
                # Statistics (To be computed)
                "stats": {
                    "total_employees": 0,
                    "active_employees": 0,
                    "sub_departments_count": len(sub_depts_by_parent.get(row["department_id"], [])),
                    "managers_count": len(owners),
                    "last_updated": datetime.utcnow(),
                },
                # System Fields
                "system": {
                    "status": row["status"],
                    "created_by": row["created_by"],
                    "updated_by": row["updated_by"],
                    "created_time": row["created_time"],
                    "updated_time": row["updated_time"],
                    "version": 1,
                },
                # Sub-departments (Embedded)
                "sub_departments": sub_depts_by_parent.get(row["department_id"], []),
                # Search Optimization
                "search": {
                    "searchable_text": f"{row['name']} {row['description'] or ''} {row['department_code'] or ''}".strip(),
                    "tags": [],
                },
            }

            departments.append(dept_doc)

        if departments:
            await self.mongo_db.departments.insert_many(departments)
            logger.info(f"Migrated {len(departments)} departments with {len(sub_dept_rows)} sub-departments")

        return {"migrated": len(departments), "sub_departments": len(sub_dept_rows), "collection": "departments"}

    async def migrate_job_titles(self) -> Dict[str, Any]:
        """Migrate job titles with embedded levels and optimized schema."""
        logger.info("Starting job_titles migration...")

        # Get job titles
        job_title_query = """
        SELECT jt.job_title_id, jt.department_id, jt.company_id, jt.name, 
               jt.lower_case_name, jt.description, jt."order", jt.status,
               jt.created_by, jt.updated_by, jt.created_time, jt.updated_time,
               d.name as department_name, d.department_code
        FROM job_title jt
        LEFT JOIN department d ON jt.department_id = d.department_id
        ORDER BY jt.created_time
        """

        # Get job title levels
        job_level_query = """
        SELECT job_title_level_id, job_title_id, company_id, name, level, 
               lower_case_name, status, created_by, updated_by, created_time, updated_time
        FROM job_title_level
        ORDER BY job_title_id, level
        """

        job_title_rows = await self.pg_conn.fetch(job_title_query)
        job_level_rows = await self.pg_conn.fetch(job_level_query)

        # Group levels by job title
        levels_by_job_title = {}
        for level in job_level_rows:
            job_title_id = level["job_title_id"]
            if job_title_id not in levels_by_job_title:
                levels_by_job_title[job_title_id] = []

            levels_by_job_title[job_title_id].append(
                {
                    "job_title_level_id": level["job_title_level_id"],
                    "identity": {
                        "name": level["name"],
                        "lower_case_name": level["lower_case_name"],
                        "level": level["level"],
                        "description": "",
                    },
                    "metrics": {
                        "employee_count": 0,  # To be computed
                        "min_salary": 0,
                        "max_salary": 0,
                        "avg_salary": 0,
                    },
                    "system": {
                        "status": level["status"],
                        "created_by": level["created_by"],
                        "updated_by": level["updated_by"],
                        "created_time": level["created_time"],
                        "updated_time": level["updated_time"],
                    },
                }
            )

        job_titles = []
        for row in job_title_rows:
            job_title_doc = {
                "job_title_id": row["job_title_id"],
                "department_id": row["department_id"],
                "company_id": row["company_id"],
                # Core Identity
                "identity": {
                    "name": row["name"],
                    "lower_case_name": row["lower_case_name"],
                    "description": row["description"],
                    "order": row["order"] or 0,
                },
                # Department Context (Denormalized for Performance)
                "department_context": {
                    "department_name": row["department_name"],
                    "department_code": row["department_code"],
                    "department_display": 1,
                },
                # Statistics (To be computed)
                "stats": {
                    "total_employees": 0,
                    "levels_count": len(levels_by_job_title.get(row["job_title_id"], [])),
                    "avg_salary": 0,
                    "last_updated": datetime.utcnow(),
                },
                # System Fields
                "system": {
                    "status": row["status"],
                    "created_by": row["created_by"],
                    "updated_by": row["updated_by"],
                    "created_time": row["created_time"],
                    "updated_time": row["updated_time"],
                    "version": 1,
                },
                # Job Title Levels (Embedded)
                "levels": levels_by_job_title.get(row["job_title_id"], []),
                # Search Optimization
                "search": {
                    "full_text": f"{row['name']} {row['description'] or ''} {row['department_name'] or ''}".strip(),
                    "tags": [],
                    "department_tags": [row["department_name"]] if row["department_name"] else [],
                },
            }

            job_titles.append(job_title_doc)

        if job_titles:
            await self.mongo_db.job_titles.insert_many(job_titles)
            logger.info(f"Migrated {len(job_titles)} job titles with {len(job_level_rows)} levels")

        return {"migrated": len(job_titles), "levels": len(job_level_rows), "collection": "job_titles"}

    async def migrate_roles(self) -> Dict[str, Any]:
        """Migrate roles with embedded permissions and optimized RBAC schema."""
        logger.info("Starting roles migration...")

        # Get roles
        role_query = """
        SELECT role_id, name, lower_case_name, description, company_id, status,
               created_by, updated_by, created_time, updated_time
        FROM "role"
        ORDER BY created_time
        """

        # Get permissions
        permission_query = """
        SELECT permission_id, company_id, description, action, scope, status,
               created_by, updated_by, created_time, updated_time
        FROM "permission"
        ORDER BY created_time
        """

        # Get role-permission mappings
        role_permission_query = """
        SELECT rp.role_permission_id, rp.role_id, rp.permission_id, rp.status,
               rp.created_by, rp.updated_by, rp.created_time, rp.updated_time
        FROM role_permission rp
        WHERE rp.status = 1
        ORDER BY rp.role_id
        """

        # Get role-policy mappings
        role_policy_query = """
        SELECT rp.role_policy_id, rp.role_id, rp.policy_id, rp.status,
               p.name as policy_name, p.policy_type
        FROM role_policies rp
        LEFT JOIN policies p ON rp.policy_id = p.policy_id
        WHERE rp.status = 1
        ORDER BY rp.role_id
        """

        role_rows = await self.pg_conn.fetch(role_query)
        permission_rows = await self.pg_conn.fetch(permission_query)
        role_permission_rows = await self.pg_conn.fetch(role_permission_query)
        role_policy_rows = await self.pg_conn.fetch(role_policy_query)

        # Create permission lookup
        permissions_by_id = {p["permission_id"]: p for p in permission_rows}

        # Group permissions by role
        permissions_by_role = {}
        for rp in role_permission_rows:
            role_id = rp["role_id"]
            permission_id = rp["permission_id"]

            if role_id not in permissions_by_role:
                permissions_by_role[role_id] = []

            if permission_id in permissions_by_id:
                perm = permissions_by_id[permission_id]
                permissions_by_role[role_id].append(
                    {
                        "permission_id": permission_id,
                        "identity": {
                            "name": f"{perm['action']}:{perm['scope']}",
                            "description": perm["description"],
                            "category": perm["scope"],
                        },
                        "access_control": {
                            "action": perm["action"],
                            "scope": perm["scope"],
                            "conditions": {},
                            "priority": 1,
                        },
                        "system": {
                            "status": perm["status"],
                            "is_system_permission": False,
                            "created_by": perm["created_by"],
                            "updated_by": perm["updated_by"],
                            "created_time": perm["created_time"],
                            "updated_time": perm["updated_time"],
                        },
                    }
                )

        # Group policies by role
        policies_by_role = {}
        for rp in role_policy_rows:
            role_id = rp["role_id"]
            if role_id not in policies_by_role:
                policies_by_role[role_id] = []

            policies_by_role[role_id].append(
                {
                    "policy_id": rp["policy_id"],
                    "policy_name": rp["policy_name"],
                    "policy_type": rp["policy_type"],
                    "is_active": rp["status"] == 1,
                }
            )

        roles = []
        for row in role_rows:
            role_permissions = permissions_by_role.get(row["role_id"], [])
            role_policies = policies_by_role.get(row["role_id"], [])

            # Create permissions map for O(1) access
            permissions_map = {}
            scopes = set()
            actions = set()

            for perm in role_permissions:
                action = perm["access_control"]["action"]
                scope = perm["access_control"]["scope"]
                key = f"{action}:{scope}"
                permissions_map[key] = True
                scopes.add(scope)
                actions.add(action)

            role_doc = {
                "role_id": row["role_id"],
                "company_id": row["company_id"],
                # Core Identity
                "identity": {
                    "name": row["name"],
                    "lower_case_name": row["lower_case_name"],
                    "description": row["description"],
                    "role_type": "custom",  # Default type
                },
                # Access Control Matrix (Optimized for Fast Permission Checks)
                "access": {
                    "permissions_map": permissions_map,
                    "scopes": list(scopes),
                    "actions": list(actions),
                    "resource_access": {},
                },
                # Statistics and Usage
                "stats": {
                    "users_count": 0,  # To be computed
                    "permissions_count": len(role_permissions),
                    "last_used": datetime.utcnow(),
                    "usage_frequency": 0,
                },
                # System Fields
                "system": {
                    "status": row["status"],
                    "is_system_role": False,
                    "created_by": row["created_by"],
                    "updated_by": row["updated_by"],
                    "created_time": row["created_time"],
                    "updated_time": row["updated_time"],
                    "version": 1,
                },
                # Permissions (Embedded for Atomic RBAC Operations)
                "permissions": role_permissions,
                # Policies (References with Denormalized Names)
                "policies": role_policies,
                # Inheritance and Hierarchy
                "inheritance": {
                    "parent_roles": [],
                    "child_roles": [],
                    "effective_permissions": [p["permission_id"] for p in role_permissions],
                },
            }

            roles.append(role_doc)

        if roles:
            await self.mongo_db.roles.insert_many(roles)
            logger.info(
                f"Migrated {len(roles)} roles with {sum(len(permissions_by_role.get(r['role_id'], [])) for r in role_rows)} permissions"
            )

        return {"migrated": len(roles), "permissions": len(permission_rows), "collection": "roles"}

    async def migrate_users(self) -> Dict[str, Any]:
        """Migrate users with ultra-optimized schema including all denormalized data."""
        logger.info("Starting users migration (optimized schema)...")

        # Complex query to get user data with all related information
        user_query = """
        SELECT u.user_id, u.employee_code, u.company_id, u.first_name, 
               u.lark_user_id, u.open_user_id, u.middle_name, u.last_name, u.name,
               u.unsigned_name, u.primary_email, u.personal_email, u.username, 
               u.password, u.gender, u.marital_status, u.leader_user_id,
               u.education_level, u.employment_type_id, u.thumb_avatar_link,
               u.icon_avatar_link, u.home_town, u.date_of_birth, u.current_address,
               u.phone_number, u.last_time_login, u.salary_amount, u.start_salary,
               u.start_onboard_at, u."order", u.contract_number, u.job_title_id,
               u.job_title_level_id, u.status, u.created_by, u.updated_by,
               u.created_time, u.updated_time,
               -- Job title information
               jt.name as job_title_name,
               jtl.name as job_level_name, jtl.level as job_level_number,
               -- Department information  
               d.department_id, d.name as department_name, d.department_code,
               -- Employment type information
               et.vi_name as employment_vi_name, et.en_name as employment_en_name,
               -- Leader information
               leader.name as leader_name, leader.employee_code as leader_employee_code
        FROM "user" u
        LEFT JOIN job_title jt ON u.job_title_id = jt.job_title_id
        LEFT JOIN job_title_level jtl ON u.job_title_level_id = jtl.job_title_level_id
        LEFT JOIN department d ON jt.department_id = d.department_id
        LEFT JOIN employment_type et ON u.employment_type_id = et.employment_type_id
        LEFT JOIN "user" leader ON u.leader_user_id = leader.user_id
        ORDER BY u.created_time
        """

        # Get user-department relationships
        user_dept_query = """
        SELECT ud.user_id, ud.department_id, d.name as department_name
        FROM user_department ud
        LEFT JOIN department d ON ud.department_id = d.department_id
        WHERE ud.status = 1
        ORDER BY ud.user_id
        """

        # Get user-role relationships
        user_role_query = """
        SELECT ur.user_id, ur.role_id, r.name as role_name
        FROM user_role ur
        LEFT JOIN "role" r ON ur.role_id = r.role_id
        WHERE ur.status = 1
        ORDER BY ur.user_id
        """

        # Get user-policy relationships
        user_policy_query = """
        SELECT up.user_id, up.policy_id
        FROM user_policies up
        WHERE up.status = 1
        ORDER BY up.user_id
        """

        user_rows = await self.pg_conn.fetch(user_query)
        user_dept_rows = await self.pg_conn.fetch(user_dept_query)
        user_role_rows = await self.pg_conn.fetch(user_role_query)
        user_policy_rows = await self.pg_conn.fetch(user_policy_query)

        # Group relationships by user
        departments_by_user = {}
        for ud in user_dept_rows:
            user_id = ud["user_id"]
            if user_id not in departments_by_user:
                departments_by_user[user_id] = []
            departments_by_user[user_id].append(
                {
                    "id": ud["department_id"],
                    "name": ud["department_name"],
                    "is_primary": len(departments_by_user[user_id]) == 0,  # First one is primary
                }
            )

        roles_by_user = {}
        for ur in user_role_rows:
            user_id = ur["user_id"]
            if user_id not in roles_by_user:
                roles_by_user[user_id] = []
            roles_by_user[user_id].append(
                {
                    "id": ur["role_id"],
                    "name": ur["role_name"],
                    "is_primary": len(roles_by_user[user_id]) == 0,
                    "permissions_count": 0,  # To be computed later
                }
            )

        policies_by_user = {}
        for up in user_policy_rows:
            user_id = up["user_id"]
            if user_id not in policies_by_user:
                policies_by_user[user_id] = []
            policies_by_user[user_id].append(up["policy_id"])

        users = []
        for row in user_rows:
            user_departments = departments_by_user.get(row["user_id"], [])
            user_roles = roles_by_user.get(row["user_id"], [])
            user_policies = policies_by_user.get(row["user_id"], [])

            # Create searchable text
            search_text_parts = [
                row["name"] or "",
                row["unsigned_name"] or "",
                row["primary_email"] or "",
                row["employee_code"] or "",
                row["job_title_name"] or "",
                row["department_name"] or "",
            ]
            search_all_text = " ".join(filter(None, search_text_parts)).lower()

            # Extract tags for faceted search
            tags = []
            if row["department_name"]:
                tags.append(row["department_name"])
            if row["job_title_name"]:
                tags.append(row["job_title_name"])
            if row["employment_vi_name"]:
                tags.append(row["employment_vi_name"])

            # Calculate profile completion (basic calculation)
            required_fields = [
                row["name"],
                row["primary_email"],
                row["phone_number"],
                row["current_address"],
                row["date_of_birth"],
            ]
            completed_fields = sum(1 for field in required_fields if field)
            profile_completion = int((completed_fields / len(required_fields)) * 100)

            user_doc = {
                "user_id": row["user_id"],
                "employee_code": row["employee_code"],
                "company_id": row["company_id"],
                # Personal Information (Optimized Structure)
                "personal": {
                    "first_name": row["first_name"],
                    "middle_name": row["middle_name"],
                    "last_name": row["last_name"],
                    "name": row["name"],
                    "unsigned_name": row["unsigned_name"],
                    "gender": row["gender"],
                    "marital_status": row["marital_status"],
                    "education_level": row["education_level"],
                    "date_of_birth": row["date_of_birth"],
                    "home_town": row["home_town"],
                },
                # Contact Information (Grouped)
                "contact": {
                    "primary_email": row["primary_email"],
                    "personal_email": row["personal_email"],
                    "phone_number": row["phone_number"],
                    "current_address": row["current_address"],
                },
                # Authentication (Separated for security)
                "auth": {
                    "username": row["username"],
                    "password": row["password"],
                    "last_time_login": row["last_time_login"],
                    "login_count": 0,
                },
                # External Integration (Lark-specific)
                "external_ids": {"lark_user_id": row["lark_user_id"], "open_user_id": row["open_user_id"]},
                # Employment Information (Heavily Denormalized for Performance)
                "employment": {
                    "type": {
                        "id": row["employment_type_id"],
                        "name": row["employment_vi_name"],
                        "vi_name": row["employment_vi_name"],
                        "en_name": row["employment_en_name"],
                    },
                    "position": {
                        "job_title_id": row["job_title_id"],
                        "job_title_name": row["job_title_name"],
                        "job_level_id": row["job_title_level_id"],
                        "job_level_name": row["job_level_name"],
                        "level_number": row["job_level_number"] or 1,
                        "department_id": row["department_id"],
                        "department_name": row["department_name"],
                        "department_code": row["department_code"],
                    },
                    "contract": {
                        "number": row["contract_number"],
                        "start_date": row["start_onboard_at"],
                        "salary_amount": float(row["salary_amount"]) if row["salary_amount"] else 0.0,
                        "start_salary_date": row["start_salary"],
                        "onboard_date": row["start_onboard_at"],
                    },
                    "hierarchy": {
                        "leader_user_id": row["leader_user_id"],
                        "leader_name": row["leader_name"],
                        "leader_employee_code": row["leader_employee_code"],
                        "is_manager": False,  # To be computed
                        "direct_reports_count": 0,  # To be computed
                        "organization_level": 1,  # To be computed
                    },
                    "order": row["order"] or 0,
                },
                # Avatar (Optimized for CDN)
                "avatar": {
                    "thumb_url": row["thumb_avatar_link"],
                    "icon_url": row["icon_avatar_link"],
                    "upload_date": datetime.utcnow(),
                },
                # System Fields
                "system": {
                    "status": row["status"],
                    "created_by": row["created_by"],
                    "updated_by": row["updated_by"],
                    "created_time": row["created_time"],
                    "updated_time": row["updated_time"],
                    "version": 1,
                },
                # Relations (Lightweight References with Denormalized Names)
                "relations": {"departments": user_departments, "roles": user_roles, "policies": user_policies},
                # Search Optimization (Pre-computed Search Fields)
                "search": {"all_text": search_all_text, "tags": tags, "boost_score": 1.0},  # Default boost score
                # Analytics Fields (for Performance Dashboards)
                "analytics": {
                    "profile_completion": profile_completion,
                    "last_active_date": row["last_time_login"] or row["updated_time"],
                    "active_days_count": 0,
                    "feature_usage": {},
                },
            }

            users.append(user_doc)

        if users:
            # Insert in batches to handle large datasets
            batch_size = 1000
            for i in range(0, len(users), batch_size):
                batch = users[i : i + batch_size]
                await self.mongo_db.users.insert_many(batch)
                logger.info(f"Inserted batch {i//batch_size + 1}: {len(batch)} users")

            logger.info(f"Migrated {len(users)} users with optimized schema")

        return {"migrated": len(users), "collection": "users"}

    async def migrate_user_events(self) -> Dict[str, Any]:
        """Migrate user_event table to separate collection."""
        logger.info("Starting user_events migration...")

        query = """
        SELECT ue.user_event_id, ue.employee_code, ue.body_event, ue.event_type,
               ue.status, ue.created_by, ue.updated_by, ue.created_time, ue.updated_time,
               u.company_id
        FROM user_event ue
        LEFT JOIN "user" u ON ue.employee_code = u.employee_code
        ORDER BY ue.created_time
        """

        rows = await self.pg_conn.fetch(query)
        events = []

        for row in rows:
            doc = {
                "company_id": row["company_id"],
                "employee_code": row["employee_code"],
                "user_event_id": row["user_event_id"],
                "body_event": row["body_event"],
                "event_type": row["event_type"],
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            events.append(doc)

        if events:
            await self.mongo_db.user_events.insert_many(events)
            logger.info(f"Migrated {len(events)} user events")

        return {"migrated": len(events), "collection": "user_events"}

    async def migrate_user_history(self) -> Dict[str, Any]:
        """Migrate user_history table to separate collection."""
        logger.info("Starting user_history migration...")

        query = """
        SELECT uh.history_id, uh.employee_code, uh.before_data, uh.after_data,
               uh.status, uh.created_by, uh.updated_by, uh.created_time, uh.updated_time,
               u.company_id
        FROM user_history uh
        LEFT JOIN "user" u ON uh.employee_code = u.employee_code
        ORDER BY uh.created_time
        """

        rows = await self.pg_conn.fetch(query)
        history = []

        for row in rows:
            # Simple change type detection
            change_type = "update"
            if not row["before_data"]:
                change_type = "create"
            elif not row["after_data"]:
                change_type = "delete"

            doc = {
                "company_id": row["company_id"],
                "employee_code": row["employee_code"],
                "history_id": row["history_id"],
                "before_data": row["before_data"],
                "after_data": row["after_data"],
                "change_type": change_type,
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            history.append(doc)

        if history:
            await self.mongo_db.user_history.insert_many(history)
            logger.info(f"Migrated {len(history)} user history records")

        return {"migrated": len(history), "collection": "user_history"}

    async def migrate_user_contacts(self) -> Dict[str, Any]:
        """Migrate contact_info table to user_contacts collection."""
        logger.info("Starting user_contacts migration...")

        query = """
        SELECT ci.contact_id, ci.company_id, ci.employee_code, ci.full_name,
               ci.email, ci.phone, ci.address, ci.relationship_type, ci.status,
               ci.created_by, ci.updated_by, ci.created_time, ci.updated_time
        FROM contact_info ci
        ORDER BY ci.created_time
        """

        rows = await self.pg_conn.fetch(query)
        contacts = []

        for row in rows:
            doc = {
                "company_id": row["company_id"],
                "employee_code": row["employee_code"],
                "contact_id": row["contact_id"],
                "full_name": row["full_name"],
                "email": row["email"],
                "phone": row["phone"],
                "address": row["address"],
                "relationship_type": row["relationship_type"],
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            contacts.append(doc)

        if contacts:
            await self.mongo_db.user_contacts.insert_many(contacts)
            logger.info(f"Migrated {len(contacts)} user contacts")

        return {"migrated": len(contacts), "collection": "user_contacts"}

    async def migrate_user_identifications(self) -> Dict[str, Any]:
        """Migrate user_identification table."""
        logger.info("Starting user_identifications migration...")

        query = """
        SELECT ui.user_identification_id, ui.company_id, ui.employee_code, ui.type,
               ui.issue_place, ui.front_image, ui.back_image, ui.identification_number,
               ui.issue_date, ui.status, ui.created_by, ui.updated_by, 
               ui.created_time, ui.updated_time
        FROM user_identification ui
        ORDER BY ui.created_time
        """

        rows = await self.pg_conn.fetch(query)
        identifications = []

        for row in rows:
            doc = {
                "company_id": row["company_id"],
                "employee_code": row["employee_code"],
                "user_identification_id": row["user_identification_id"],
                "type": row["type"],
                "issue_place": row["issue_place"],
                "front_image": row["front_image"],
                "back_image": row["back_image"],
                "identification_number": row["identification_number"],
                "issue_date": row["issue_date"],
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            identifications.append(doc)

        if identifications:
            await self.mongo_db.user_identifications.insert_many(identifications)
            logger.info(f"Migrated {len(identifications)} user identifications")

        return {"migrated": len(identifications), "collection": "user_identifications"}

    async def create_indexes(self):
        """Create all optimized indexes based on schema requirements."""
        logger.info("Creating MongoDB indexes...")

        index_operations = []

        # Companies indexes
        index_operations.extend(
            [
                ("companies", [("company_id", 1)], {"unique": True}),
                ("companies", [("tenant_key", 1)], {}),
            ]
        )

        # Users ultra-optimized indexes
        index_operations.extend(
            [
                ("users", [("user_id", 1)], {"unique": True}),
                ("users", [("employee_code", 1)], {"unique": True}),
                ("users", [("contact.primary_email", 1)], {"unique": True}),
                ("users", [("auth.username", 1)], {"unique": True, "sparse": True}),
                ("users", [("company_id", 1), ("system.status", 1)], {}),
                ("users", [("company_id", 1), ("employment.position.department_id", 1), ("system.status", 1)], {}),
                ("users", [("company_id", 1), ("employment.hierarchy.leader_user_id", 1)], {}),
                ("users", [("employment.position.job_title_id", 1), ("system.status", 1)], {}),
                ("users", [("employment.position.job_level_id", 1)], {}),
                ("users", [("employment.contract.number", 1)], {"sparse": True}),
                ("users", [("external_ids.lark_user_id", 1)], {"sparse": True}),
                ("users", [("external_ids.open_user_id", 1)], {"sparse": True}),
                ("users", [("company_id", 1), ("auth.last_time_login", -1)], {}),
                ("users", [("company_id", 1), ("system.created_time", -1)], {}),
                ("users", [("company_id", 1), ("employment.hierarchy.is_manager", 1)], {}),
                ("users", [("company_id", 1), ("relations.roles.id", 1), ("system.status", 1)], {}),
                ("users", [("company_id", 1), ("employment.position.level_number", 1)], {}),
                ("users", [("analytics.last_active_date", -1)], {}),
            ]
        )

        # Departments repository-optimized indexes
        index_operations.extend(
            [
                ("departments", [("department_id", 1)], {"unique": True}),
                ("departments", [("company_id", 1), ("system.status", 1)], {}),
                ("departments", [("company_id", 1), ("identity.name", 1)], {"unique": True}),
                ("departments", [("company_id", 1), ("identity.lower_case_name", 1)], {"unique": True}),
                ("departments", [("company_id", 1), ("identity.display", 1), ("hierarchy.order", 1)], {}),
                ("departments", [("external_ids.lark_department_id", 1)], {"sparse": True}),
                ("departments", [("hierarchy.owners", 1)], {}),
                ("departments", [("hierarchy.parent_department_id", 1)], {}),
                ("departments", [("sub_departments.code", 1)], {"sparse": True}),
                ("departments", [("company_id", 1), ("sub_departments.status", 1)], {}),
            ]
        )

        # Job titles repository-pattern optimized indexes
        index_operations.extend(
            [
                ("job_titles", [("job_title_id", 1)], {"unique": True}),
                ("job_titles", [("company_id", 1), ("department_id", 1), ("system.status", 1)], {}),
                ("job_titles", [("department_id", 1), ("system.status", 1), ("identity.order", 1)], {}),
                ("job_titles", [("levels.job_title_level_id", 1)], {"unique": True}),
                ("job_titles", [("company_id", 1), ("levels.identity.level", 1), ("levels.system.status", 1)], {}),
                ("job_titles", [("levels.identity.name", 1)], {}),
                ("job_titles", [("department_id", 1), ("levels.identity.level", 1)], {}),
                ("job_titles", [("company_id", 1), ("stats.total_employees", -1)], {}),
            ]
        )

        # Roles RBAC-optimized indexes
        index_operations.extend(
            [
                ("roles", [("role_id", 1)], {"unique": True}),
                ("roles", [("company_id", 1), ("identity.name", 1)], {"unique": True}),
                ("roles", [("company_id", 1), ("identity.lower_case_name", 1)], {"unique": True}),
                ("roles", [("company_id", 1), ("system.status", 1), ("identity.role_type", 1)], {}),
                ("roles", [("permissions.permission_id", 1)], {}),
                ("roles", [("permissions.access_control.action", 1), ("permissions.access_control.scope", 1)], {}),
                ("roles", [("company_id", 1), ("permissions.access_control.action", 1)], {}),
                ("roles", [("access.scopes", 1)], {}),
                ("roles", [("company_id", 1), ("stats.users_count", -1)], {}),
                ("roles", [("inheritance.parent_roles", 1)], {}),
                ("roles", [("policies.policy_id", 1), ("policies.is_active", 1)], {}),
            ]
        )

        # Policies indexes
        index_operations.extend(
            [
                ("policies", [("policy_id", 1)], {"unique": True}),
                ("policies", [("company_id", 1), ("name", 1)], {"unique": True}),
                ("policies", [("company_id", 1), ("policy_type", 1), ("status", 1)], {}),
                ("policies", [("company_id", 1), ("status", 1)], {}),
            ]
        )

        # Contracts indexes
        index_operations.extend(
            [
                ("contracts", [("contract_number", 1)], {"unique": True, "sparse": True}),
                ("contracts", [("company_id", 1), ("status", 1)], {}),
                ("contracts", [("company_id", 1), ("created_time", -1)], {}),
            ]
        )

        # Employment types indexes
        index_operations.extend(
            [
                ("employment_types", [("employment_type_id", 1)], {"unique": True}),
                ("employment_types", [("company_id", 1), ("status", 1)], {}),
            ]
        )

        # Branches indexes
        index_operations.extend(
            [
                ("branches", [("branch_id", 1)], {"unique": True}),
                ("branches", [("company_id", 1), ("status", 1)], {}),
                ("branches", [("company_id", 1), ("code", 1)], {"unique": True}),
            ]
        )

        # User events indexes
        index_operations.extend(
            [
                ("user_events", [("company_id", 1), ("employee_code", 1), ("created_time", -1)], {}),
                ("user_events", [("company_id", 1), ("event_type", 1), ("created_time", -1)], {}),
                ("user_events", [("employee_code", 1), ("status", 1)], {}),
            ]
        )

        # User history indexes
        index_operations.extend(
            [
                ("user_history", [("company_id", 1), ("employee_code", 1), ("created_time", -1)], {}),
                ("user_history", [("employee_code", 1), ("change_type", 1), ("created_time", -1)], {}),
                ("user_history", [("created_by", 1), ("created_time", -1)], {}),
            ]
        )

        # User contacts indexes
        index_operations.extend(
            [
                ("user_contacts", [("company_id", 1), ("employee_code", 1), ("status", 1)], {}),
                ("user_contacts", [("employee_code", 1), ("relationship_type", 1)], {}),
            ]
        )

        # User identifications indexes
        index_operations.extend(
            [
                ("user_identifications", [("company_id", 1), ("employee_code", 1), ("status", 1)], {}),
                ("user_identifications", [("identification_number", 1)], {"unique": True, "sparse": True}),
                ("user_identifications", [("type", 1), ("issue_place", 1)], {}),
            ]
        )

        # Create text indexes for search
        text_indexes = [
            ("users", [("personal.unsigned_name", "text"), ("search.all_text", "text")], {}),
            ("departments", [("identity.lower_case_name", "text"), ("search.searchable_text", "text")], {}),
            ("job_titles", [("identity.name", "text"), ("search.full_text", "text")], {}),
        ]

        # Execute index creation
        for collection_name, index_spec, options in index_operations:
            try:
                await self.mongo_db[collection_name].create_index(index_spec, **options)
                logger.info(f"Created index on {collection_name}: {index_spec}")
            except Exception as e:
                logger.warning(f"Index creation failed for {collection_name} {index_spec}: {e}")

        # Create text indexes separately
        for collection_name, index_spec, options in text_indexes:
            try:
                await self.mongo_db[collection_name].create_index(index_spec, **options)
                logger.info(f"Created text index on {collection_name}: {index_spec}")
            except Exception as e:
                logger.warning(f"Text index creation failed for {collection_name}: {e}")

        logger.info("Index creation completed")

    async def run_full_migration(self) -> Dict[str, Any]:
        """Execute complete migration from PostgreSQL to MongoDB."""
        logger.info("Starting full migration from PostgreSQL to MongoDB...")
        start_time = datetime.utcnow()

        try:
            # Migration order is important due to dependencies
            migration_steps = [
                ("companies", self.migrate_companies),
                # ("employment_types", self.migrate_employment_types),
                # ("contracts", self.migrate_contracts),
                # ("branches", self.migrate_branches),
                # ("policies", self.migrate_policies),
                # ("departments", self.migrate_departments),
                # ("job_titles", self.migrate_job_titles),
                # ("roles", self.migrate_roles),
                # ("users", self.migrate_users),
                # ("user_events", self.migrate_user_events),
                # ("user_history", self.migrate_user_history),
                # ("user_contacts", self.migrate_user_contacts),
                # ("user_identifications", self.migrate_user_identifications),
            ]

            migration_results = {}

            for step_name, migration_func in migration_steps:
                try:
                    logger.info(f"Starting {step_name} migration...")
                    result = await migration_func()
                    migration_results[step_name] = result
                    logger.info(f"Completed {step_name} migration: {result}")
                except Exception as e:
                    logger.error(f"Failed to migrate {step_name}: {e}")
                    migration_results[step_name] = {"error": str(e), "migrated": 0}

            # Create indexes after all data is migrated
            # await self.create_indexes()

            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()

            # Calculate totals
            total_migrated = sum(result.get("migrated", 0) for result in migration_results.values())

            summary = {
                "status": "completed",
                "start_time": start_time,
                "end_time": end_time,
                "duration_seconds": duration,
                "total_records_migrated": total_migrated,
                "collections_migrated": len(migration_results),
                "results": migration_results,
            }

            logger.info(f"Migration completed successfully in {duration:.2f} seconds")
            logger.info(f"Total records migrated: {total_migrated}")

            return summary

        except Exception as e:
            logger.error(f"Migration failed: {e}")
            raise


async def main():
    """Main migration function."""
    import argparse

    parser = argparse.ArgumentParser(description="Migrate HR data from PostgreSQL to MongoDB")
    parser.add_argument("--postgres-url", required=True, help="PostgreSQL connection URL")
    parser.add_argument("--mongo-url", required=True, help="MongoDB connection URL")
    parser.add_argument("--mongo-db", required=True, help="MongoDB database name")
    parser.add_argument("--drop-existing", action="store_true", help="Drop existing collections before migration")

    args = parser.parse_args()

    async with SQLToMongoMigrator(args.postgres_url, args.mongo_url, args.mongo_db) as migrator:
        if args.drop_existing:
            logger.info("Dropping existing collections...")
            collections = await migrator.mongo_db.list_collection_names()
            for collection in collections:
                await migrator.mongo_db[collection].drop()
                logger.info(f"Dropped collection: {collection}")

        # Run migration
        result = await migrator.run_full_migration()

        # Print summary
        print("\n" + "=" * 50)
        print("MIGRATION SUMMARY")
        print("=" * 50)
        print(f"Status: {result['status']}")
        print(f"Duration: {result['duration_seconds']:.2f} seconds")
        print(f"Total records: {result['total_records_migrated']}")
        print(f"Collections: {result['collections_migrated']}")

        for collection, stats in result["results"].items():
            if "error" in stats:
                print(f"❌ {collection}: ERROR - {stats['error']}")
            else:
                print(f"✅ {collection}: {stats['migrated']} records")

        print("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())
