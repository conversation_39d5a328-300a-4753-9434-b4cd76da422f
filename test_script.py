#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 17/06/2025
"""
if __name__ == "__main__":
    x = {
            "evaluate_period": {
                "start_time": "2025-06-16T17:01Z",
                "end_time": "2025-06-22T16:59Z",
                "repeat_type": "once",
                "config": {"employee_evaluate_interval_day": 3, "leader_evaluate_interval_day": 3},
                "exclude_department_ids": [],
                "specific_department_ids": [],
                "name": "T<PERSON>t cả các team - 17/6 - 22/6 tháng 6/2025",
                "start_time_aggregate_performance": "2025-01-01T00:00Z",
                "end_time_aggregate_performance": "2025-06-30T23:59Z",
                "company_id": "4302feda-826d-4419-b1f5-c9a8291ffb51",
                "created_time": "2025-06-17T06:57Z",
                "updated_time": "2025-06-17T06:57Z",
                "created_by": "admin",
                "updated_by": "admin",
                "status": 1,
                "month_review": "number_month_six",
                "larkbase_summary_performance": {
                    "url": "https://ojtxyqaavqd.sg.larksuite.com/wiki/R8r4wsAJcilSoUk9psnlRSXkgCd",
                    "app_token": None,
                    "folder_token": None,
                    "table_id": "tblpBP5aq956zrKS",
                },
                "id": "685111c7d2e84c1a6213cd4d",
            },
            "evaluate": {
                "company_id": "4302feda-826d-4419-b1f5-c9a8291ffb51",
                "department_id": "ed1b9778-0363-42af-acb3-876104872fdf",
                "user_id": "b2c2275c-1f97-47ff-a577-f8e8b4c9f687",
                "start_time": "2025-06-16T17:01Z",
                "end_time": "2025-06-22T16:59Z",
                "evaluate_period_id": "685111c7d2e84c1a6213cd4d",
                "competency_groups": [
                    {
                        "name": "Nhóm năng lực bổ trợ",
                        "lst_competency": [
                            {
                                "name": "NL chưa áp dụng cho TEST",
                                "description": "",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": "a",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "b",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": -1,
                                    },
                                ],
                                "pask_code": "personality",
                                "reference_info": None,
                                "is_default_apply": True,
                                "competency_default_id": "6838e6c809bf127163307815",
                                "competency_reference_id": None,
                                "competency_id": "c0483c006f0c421d8805ed78",
                                "point_min": 0,
                            }
                        ],
                        "is_default": False,
                        "competency_group_default_id": None,
                        "competency_group_id": "5de22f9d911049fbaffceaaf",
                    },
                    {
                        "name": "Nhóm năng lực chuyên môn",
                        "lst_competency": [
                            {
                                "name": "Kỹ năng viết tài liệu",
                                "description": "Công việc chiếm 30 -40% thời gian trong 1 tháng là viết \n- Viết tài liệu\n- Viết meeting note\n- Viết phản hồi đánh giá kết quả phân tích  \nNên viết là kỹ năng rất quan trọng trong bộ kỹ năng chuyên môn của BA",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 5,
                                        "is_activate": True,
                                        "point_min": 5,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": -1,
                                    },
                                ],
                                "pask_code": "skill",
                                "reference_info": "NL của NLCM",
                                "is_default_apply": True,
                                "competency_default_id": "67f5044a102ba233105ab37d",
                                "competency_reference_id": None,
                                "competency_id": "18cdca0cc13a4ecfa8ac58e3",
                                "point_min": 2,
                            },
                            {
                                "name": "Kỹ Năng Tư Duy Hệ Thống (System Thinking)",
                                "description": "",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": " Có nhận thức về System Thinking\n- Có kỹ năng/thói quen thu thập thông tin tổng thể trước khi nhận nhiệm vụ, hoặc trước khi bắt đầu làm/xử lý công việc.\n- Có thói quen chủ động đặt câu hỏi (áp dụng 5W1H) để có đầy đủ thông tin về việc cần phải làm, vấn đề cần phải giải quyết...hoặc trong lúc tham gia vào brainstorm nhận diện vấn đề.",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": " Có nhận thức về System Thinking\n- Có kỹ năng/thói quen thu thập thông tin tổng thể trước khi nhận nhiệm vụ, hoặc trước khi bắt đầu làm/xử lý công việc.\n- Có thói quen chủ động đặt câu hỏi (áp dụng 5W1H) để có đầy đủ thông tin về việc cần phải làm, vấn đề cần phải giải quyết...hoặc trong lúc tham gia vào brainstorm nhận diện vấn đề.",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Vẽ được sơ đồ ảnh hưởng/phụ thuộc của các thành phần trong system. Áp dụng cho mọi System làm việc.\n- Nhận thức và vẽ được sơ đồ tác động, sơ đồ ảnh hương/phụ thuộc của complex system (3-5 loops)\n- Đánh giá được cái giá phải trả của các thành phần khi nâng/hạ mục tiêu đạt được của toàn hệ thống.\n- Diễn đạt, giải thích được vận hành của System.",
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Sử dụng thành thục và thường xuyên.\n- Lan toả cách áp dụng.\n- Đào tạo/hướng dẫn được cho các member khác, nhân sự khác về cách sử dụng System Thinking.",
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "skill",
                                "reference_info": "",
                                "is_default_apply": True,
                                "competency_default_id": "67f88cf425922ca07396c7b2",
                                "competency_reference_id": None,
                                "competency_id": "83ab7010fe5b4056b0f8dbbc",
                                "point_min": 2,
                            },
                        ],
                        "is_default": False,
                        "competency_group_default_id": None,
                        "competency_group_id": "2a30689f09904346a23219fd",
                    },
                    {
                        "name": "Nhóm năng lực cốt lõi",
                        "lst_competency": [
                            {
                                "name": "NL 2",
                                "description": "wqrqr",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": "rqwrwq",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "rwqrqw",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "rqwrqw",
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "rwqr",
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "qwrqw",
                                        "level": 5,
                                        "is_activate": True,
                                        "point_min": 5,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 4,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 5,
                                    },
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "personality",
                                "reference_info": None,
                                "is_default_apply": True,
                                "competency_default_id": "6837e39419f3c20fd1306324",
                                "competency_reference_id": None,
                                "competency_id": "dfc05cc3addf47f99a1243e6",
                                "point_min": 3,
                            }
                        ],
                        "is_default": False,
                        "competency_group_default_id": None,
                        "competency_group_id": "31cc95b5665d44ea9aedb973",
                    },
                    {
                        "name": "Nhóm năng lực bổ trợ",
                        "lst_competency": [
                            {
                                "name": "NL chưa áp dụng cho TEST",
                                "description": "",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": "a",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "b",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 2,
                                    },
                                ],
                                "pask_code": "personality",
                                "reference_info": None,
                                "is_default_apply": True,
                                "competency_default_id": "6838e6c809bf127163307815",
                                "competency_reference_id": None,
                                "competency_id": "b8b1e1d6cbc64f04b402414e",
                                "point_min": 1,
                            }
                        ],
                        "is_default": False,
                        "competency_group_default_id": None,
                        "competency_group_id": "9b76f150f12d45efa38ad972",
                    },
                    {
                        "name": "Nhóm năng lực chuyên môn",
                        "lst_competency": [
                            {
                                "name": "Kỹ năng viết tài liệu",
                                "description": "Công việc chiếm 30 -40% thời gian trong 1 tháng là viết \n- Viết tài liệu\n- Viết meeting note\n- Viết phản hồi đánh giá kết quả phân tích  \nNên viết là kỹ năng rất quan trọng trong bộ kỹ năng chuyên môn của BA",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 5,
                                        "is_activate": True,
                                        "point_min": 5,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 4,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "skill",
                                "reference_info": "NL của NLCM",
                                "is_default_apply": True,
                                "competency_default_id": "67f5044a102ba233105ab37d",
                                "competency_reference_id": None,
                                "competency_id": "c5d21d19dafa493aac49e6ce",
                                "point_min": 2,
                            },
                            {
                                "name": "Kỹ Năng Tư Duy Hệ Thống (System Thinking)",
                                "description": "",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": " Có nhận thức về System Thinking\n- Có kỹ năng/thói quen thu thập thông tin tổng thể trước khi nhận nhiệm vụ, hoặc trước khi bắt đầu làm/xử lý công việc.\n- Có thói quen chủ động đặt câu hỏi (áp dụng 5W1H) để có đầy đủ thông tin về việc cần phải làm, vấn đề cần phải giải quyết...hoặc trong lúc tham gia vào brainstorm nhận diện vấn đề.",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": " Có nhận thức về System Thinking\n- Có kỹ năng/thói quen thu thập thông tin tổng thể trước khi nhận nhiệm vụ, hoặc trước khi bắt đầu làm/xử lý công việc.\n- Có thói quen chủ động đặt câu hỏi (áp dụng 5W1H) để có đầy đủ thông tin về việc cần phải làm, vấn đề cần phải giải quyết...hoặc trong lúc tham gia vào brainstorm nhận diện vấn đề.",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Vẽ được sơ đồ ảnh hưởng/phụ thuộc của các thành phần trong system. Áp dụng cho mọi System làm việc.\n- Nhận thức và vẽ được sơ đồ tác động, sơ đồ ảnh hương/phụ thuộc của complex system (3-5 loops)\n- Đánh giá được cái giá phải trả của các thành phần khi nâng/hạ mục tiêu đạt được của toàn hệ thống.\n- Diễn đạt, giải thích được vận hành của System.",
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Sử dụng thành thục và thường xuyên.\n- Lan toả cách áp dụng.\n- Đào tạo/hướng dẫn được cho các member khác, nhân sự khác về cách sử dụng System Thinking.",
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 4,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "skill",
                                "reference_info": "",
                                "is_default_apply": True,
                                "competency_default_id": "67f88cf425922ca07396c7b2",
                                "competency_reference_id": None,
                                "competency_id": "a7f1f77bffb5401da22d038c",
                                "point_min": 2,
                            },
                        ],
                        "is_default": False,
                        "competency_group_default_id": None,
                        "competency_group_id": "180a48c0f994409d9e0b8eac",
                    },
                    {
                        "name": "Nhóm năng lực cốt lõi",
                        "lst_competency": [
                            {
                                "name": "NL 2",
                                "description": "wqrqr",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": "rqwrwq",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "rwqrqw",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "rqwrqw",
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "rwqr",
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "qwrqw",
                                        "level": 5,
                                        "is_activate": True,
                                        "point_min": 5,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "personality",
                                "reference_info": None,
                                "is_default_apply": True,
                                "competency_default_id": "6837e39419f3c20fd1306324",
                                "competency_reference_id": None,
                                "competency_id": "341a3fde0bbf4c02b1518938",
                                "point_min": 2,
                            }
                        ],
                        "is_default": False,
                        "competency_group_default_id": None,
                        "competency_group_id": "e1c8a75e210d4b17ab29a68c",
                    },
                    {
                        "name": "Nhóm năng lực bổ trợ",
                        "lst_competency": [
                            {
                                "name": "NL chưa áp dụng cho TEST",
                                "description": "",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": "a",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "b",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 2,
                                    },
                                ],
                                "pask_code": "personality",
                                "reference_info": None,
                                "is_default_apply": True,
                                "competency_default_id": "6838e6c809bf127163307815",
                                "competency_reference_id": None,
                                "competency_id": "22a1b4b9c71e40549872f0d3",
                                "point_min": 0,
                            }
                        ],
                        "is_default": False,
                        "competency_group_default_id": None,
                        "competency_group_id": "36b5e98b5aee4ed687fed32a",
                    },
                    {
                        "name": "Nhóm năng lực chuyên môn",
                        "lst_competency": [
                            {
                                "name": "Kỹ năng viết tài liệu",
                                "description": "Công việc chiếm 30 -40% thời gian trong 1 tháng là viết \n- Viết tài liệu\n- Viết meeting note\n- Viết phản hồi đánh giá kết quả phân tích  \nNên viết là kỹ năng rất quan trọng trong bộ kỹ năng chuyên môn của BA",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 5,
                                        "is_activate": True,
                                        "point_min": 5,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "skill",
                                "reference_info": "NL của NLCM",
                                "is_default_apply": True,
                                "competency_default_id": "67f5044a102ba233105ab37d",
                                "competency_reference_id": None,
                                "competency_id": "6ae8e6de0f104b57829f7930",
                                "point_min": 2,
                            },
                            {
                                "name": "Kỹ Năng Tư Duy Hệ Thống (System Thinking)",
                                "description": "",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": " Có nhận thức về System Thinking\n- Có kỹ năng/thói quen thu thập thông tin tổng thể trước khi nhận nhiệm vụ, hoặc trước khi bắt đầu làm/xử lý công việc.\n- Có thói quen chủ động đặt câu hỏi (áp dụng 5W1H) để có đầy đủ thông tin về việc cần phải làm, vấn đề cần phải giải quyết...hoặc trong lúc tham gia vào brainstorm nhận diện vấn đề.",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": " Có nhận thức về System Thinking\n- Có kỹ năng/thói quen thu thập thông tin tổng thể trước khi nhận nhiệm vụ, hoặc trước khi bắt đầu làm/xử lý công việc.\n- Có thói quen chủ động đặt câu hỏi (áp dụng 5W1H) để có đầy đủ thông tin về việc cần phải làm, vấn đề cần phải giải quyết...hoặc trong lúc tham gia vào brainstorm nhận diện vấn đề.",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Vẽ được sơ đồ ảnh hưởng/phụ thuộc của các thành phần trong system. Áp dụng cho mọi System làm việc.\n- Nhận thức và vẽ được sơ đồ tác động, sơ đồ ảnh hương/phụ thuộc của complex system (3-5 loops)\n- Đánh giá được cái giá phải trả của các thành phần khi nâng/hạ mục tiêu đạt được của toàn hệ thống.\n- Diễn đạt, giải thích được vận hành của System.",
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Sử dụng thành thục và thường xuyên.\n- Lan toả cách áp dụng.\n- Đào tạo/hướng dẫn được cho các member khác, nhân sự khác về cách sử dụng System Thinking.",
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "skill",
                                "reference_info": "",
                                "is_default_apply": True,
                                "competency_default_id": "67f88cf425922ca07396c7b2",
                                "competency_reference_id": None,
                                "competency_id": "2f0f13788c0c419c93293868",
                                "point_min": 2,
                            },
                        ],
                        "is_default": False,
                        "competency_group_default_id": None,
                        "competency_group_id": "faa8d9b5173d4e7eb8c45183",
                    },
                    {
                        "name": "Nhóm năng lực cốt lõi",
                        "lst_competency": [
                            {
                                "name": "NL 2",
                                "description": "wqrqr",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": "rqwrwq",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "rwqrqw",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "rqwrqw",
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "rwqr",
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "qwrqw",
                                        "level": 5,
                                        "is_activate": True,
                                        "point_min": 5,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "personality",
                                "reference_info": None,
                                "is_default_apply": True,
                                "competency_default_id": "6837e39419f3c20fd1306324",
                                "competency_reference_id": None,
                                "competency_id": "659bab1b1e554ef89d54468b",
                                "point_min": 2,
                            }
                        ],
                        "is_default": False,
                        "competency_group_default_id": None,
                        "competency_group_id": "d0004f79fcf446f59c451c00",
                    },
                    {
                        "name": "Nhóm năng lực chuyên môn",
                        "lst_competency": [
                            {
                                "name": "Kỹ năng viết tài liệu",
                                "description": "Công việc chiếm 30 -40% thời gian trong 1 tháng là viết \n- Viết tài liệu\n- Viết meeting note\n- Viết phản hồi đánh giá kết quả phân tích  \nNên viết là kỹ năng rất quan trọng trong bộ kỹ năng chuyên môn của BA",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Tổ chức tài liệu, trình bày logic khoa học\n- Cần kỹ năng viết đúng chính tả, ngữ pháp, mạch lạc, dễ hiểu, đáp ứng một số tiêu chuẩn phổ thông đối với các hình thức cơ bản thường gặp trong quá trình làm việc: Email, Biên bản, thông báo, hợp đồng.\n- Viết xong đọc đi đọc lại thấy không chản thì ổn.",
                                        "level": 5,
                                        "is_activate": True,
                                        "point_min": 5,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 4,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 4,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "skill",
                                "reference_info": "NL của NLCM",
                                "is_default_apply": True,
                                "competency_default_id": "67f5044a102ba233105ab37d",
                                "competency_reference_id": None,
                                "competency_id": "ee2ae65fc9744d938ab9e270",
                                "point_min": 2,
                            },
                            {
                                "name": "Kỹ Năng Tư Duy Hệ Thống (System Thinking)",
                                "description": "",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": " Có nhận thức về System Thinking\n- Có kỹ năng/thói quen thu thập thông tin tổng thể trước khi nhận nhiệm vụ, hoặc trước khi bắt đầu làm/xử lý công việc.\n- Có thói quen chủ động đặt câu hỏi (áp dụng 5W1H) để có đầy đủ thông tin về việc cần phải làm, vấn đề cần phải giải quyết...hoặc trong lúc tham gia vào brainstorm nhận diện vấn đề.",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": " Có nhận thức về System Thinking\n- Có kỹ năng/thói quen thu thập thông tin tổng thể trước khi nhận nhiệm vụ, hoặc trước khi bắt đầu làm/xử lý công việc.\n- Có thói quen chủ động đặt câu hỏi (áp dụng 5W1H) để có đầy đủ thông tin về việc cần phải làm, vấn đề cần phải giải quyết...hoặc trong lúc tham gia vào brainstorm nhận diện vấn đề.",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Vẽ được sơ đồ ảnh hưởng/phụ thuộc của các thành phần trong system. Áp dụng cho mọi System làm việc.\n- Nhận thức và vẽ được sơ đồ tác động, sơ đồ ảnh hương/phụ thuộc của complex system (3-5 loops)\n- Đánh giá được cái giá phải trả của các thành phần khi nâng/hạ mục tiêu đạt được của toàn hệ thống.\n- Diễn đạt, giải thích được vận hành của System.",
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Sử dụng thành thục và thường xuyên.\n- Lan toả cách áp dụng.\n- Đào tạo/hướng dẫn được cho các member khác, nhân sự khác về cách sử dụng System Thinking.",
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 5,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "skill",
                                "reference_info": "https://www.youtube.com/watch?v=bNASybOzruM&pp=ygUYc3lzdGVtIHRoaW5raW5nIHRlZCB0YWxr",
                                "is_default_apply": True,
                                "competency_default_id": "67f88cf425922ca07396c7b2",
                                "competency_reference_id": None,
                                "competency_id": "7b48c4aa1ecd4bc5bfb33645",
                                "point_min": 1,
                            },
                        ],
                        "is_default": False,
                        "competency_group_default_id": None,
                        "competency_group_id": "5087a96d8a88418eaa6abaf5",
                    },
                    {
                        "name": "Nhóm năng lực cốt lõi về con người",
                        "lst_competency": [
                            {
                                "name": "Trách nhiệm",
                                "description": "- Trách nhiệm trong công việc\n- Trách nhiệm với tập thể ",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": " - Có tinh thần nhận trách nhiệm với những công việc mình phụ trách, dù kết quả tích cực hay tiêu cực.\n- Khi không thể hoàn thành đúng hạn, thì phải chủ động thông báo với team mate, quản lý và nêu rõ khó khăn tại sao lại bị trượt deadline, và cần sự hỗ trợ gì để hoàn thành deadline. \n- Có ý thức thực hiện và tuân thủ các chính sách, quy định, hướng dẫn của công ty, của đội nhóm. ",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Không dừng lại ở việc hoàn thành công việc được giao mà có tinh thần chủ động và trách nhiệm giúp cho kết quả hoàn thành tốt hơn \n- Sẵn sàng đứng ra nhận công việc, cơ hội cũng như trách nhiệm mới.\n- Chủ động đề xuất việc gì cần phải làm để đạt được mục tiêu chung của cả nhóm, cả công ty.\n- Chủ động học hỏi và cải tiến cách thức làm việc để nâng cao chất lượng đầu ra công việc.\n- Tích cực hỗ trợ công việc của các thành viên khác.",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Chủ động nhìn nhận, đánh giá và đưa ra công việc cần phải làm để hướng tới hiệu quả công việc cao hơn hoặc hướng tới đạt mục tiêu tổ chức. Không để cái tôi hay vấn đề cá nhân ảnh hưởng đến chất lượng công việc hay mục tiêu chung.\n- Liên tục nâng cao tiêu chuẩn chất lượng đầu ra công việc, đưa ra những mục tiêu thách thức hơn để tạo ra những cơ hội mới cho bản thân và tổ chức.",
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 4,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 5,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "2ec1e676-d7c3-467b-8463-680d01ad007f",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 3,
                                    },
                                ],
                                "pask_code": "personality",
                                "reference_info": "Tham khảo khoá học X",
                                "is_default_apply": False,
                                "competency_default_id": None,
                                "competency_reference_id": None,
                                "competency_id": "bb692242ba8c407c8284638e",
                                "point_min": 1,
                            },
                            {
                                "name": "Tôn trọng",
                                "description": "- Tôn trọng tổ chức\n- Tôn trọng đồng nghiệp\n- Tôn trọng khách hàng, đối tác",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": '"- Có ý thức thực hiện đúng và đủ những quy định, trách nhiệm chung của đội nhóm, công ty.\n- Chủ động lắng nghe, bày tỏ ý kiến và quan điểm một cách rõ ràng, khách quan, mang tính chất xây dựng và hướng tới sự cải tiến chung."',
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Coi trọng văn hoá doanh nghiệp, góp phần trong việc lan toả, giúp mọi người hiểu đúng đủ về thái độ tôn trọng và văn hoá doanh nghiệp của Mobio.\n - Nhiệt tình và tích cực thực hiện, đóng góp trong các hoạt động chung, các yêu cầu, quy định chính sách của công ty."',
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Thấu hiểu, coi trọng và lan toả được văn hoá doanh nghiệp.\n - Có khả năng tác động, tạo ảnh hưởng tích cực tới các thành viên khác để tạo ra một môi trường tôn trọng, cởi mở và trung thực.\n-  Có tinh thần và can đảm để góp ý, nêu ý kiến và chấn chỉnh nếu nhìn thấy yếu tố không tôn trọng trong các thành viên của công ty (bất kể team nào)."',
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 4,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 5,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 3,
                                    },
                                ],
                                "pask_code": "attitude",
                                "reference_info": None,
                                "is_default_apply": False,
                                "competency_default_id": None,
                                "competency_reference_id": None,
                                "competency_id": "a0d8424e69ab43be901309ce",
                                "point_min": 1,
                            },
                            {
                                "name": "Không ngừng  học hỏi/ cầu tiến",
                                "description": "- Học hỏi kiến thức mới, tư duy mới\n- Cầu tiến về năng lực cá nhân\n- Cầu tiến về cơ hội thăng tiến",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": '"- Kiên trì, chịu khó, sẵn sàng tiếp nhận công việc.\n- Nghiêm túc hoàn thành lộ trình đào tạo, các yêu cầu công việc đặt ra.\n- Nhìn nhận và hiểu công việc vừa là trách nhiệm, vừa là cơ hội."',
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Tư duy tích cực, bền bỉ \n- Có khả năng nhìn nhận và thừa nhận điểm yếu của bản thân, biết cách đánh giá tình huống và đề nghị sự hỗ trợ của các thành viên khác khi cần thiết\n- Chủ động quan sát và có khả năng nhìn nhận, học hỏi từ cách thức làm việc của những thành viên khác.\n- Chủ động đọc và học từ những tài liệu, tư liệu có sẵn của công ty\n"',
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Biết cách điều chỉnh để cải thiện điểm yếu & phát huy điểm mạnh của bản thân một cách hiệu quả.  \n- Giữ tư duy đổi mới, cải tiến cách thức làm việc để đạt hiệu quả cao hơn trong mọi công việc mình làm, dù là những công việc nhỏ nhất.\n- Không chỉ nhìn vào con đường phát triển của bản thân, mà còn có khả năng nhìn nhận, đánh giá, đưa ra feedback có tính xây dựng cho các thành viên khác.\n- Chủ động tìm tòi học hỏi, trau dồi kiến thức mới\n"',
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 4,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 5,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 3,
                                    },
                                ],
                                "pask_code": "attitude",
                                "reference_info": None,
                                "is_default_apply": False,
                                "competency_default_id": None,
                                "competency_reference_id": None,
                                "competency_id": "6f53dba6e0794213b26fbb1a",
                                "point_min": 2,
                            },
                            {
                                "name": "Tư duy Logic",
                                "description": "- Logic trong cách trình bày, diễn đạt và lập luận \n- Tư duy logic trong cách trình bày, tổ chức tài liệu viết",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": '"\n- Trình bày và diễn đạt dễ hiểu "',
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Có khả năng sắp xếp và phân loại tiểu tiết của vấn đề để hiểu bản chất của vấn đề. \n- Có khả năng bao quát vấn đề tổng quan"',
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Có khả năng lập luận phát triển ý \n- Tư duy theo quy trình, ngoại lệ"',
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 4,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 5,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 3,
                                    },
                                ],
                                "pask_code": "personality",
                                "reference_info": None,
                                "is_default_apply": False,
                                "competency_default_id": None,
                                "competency_reference_id": None,
                                "competency_id": "a122f51f52d547a2abe2981c",
                                "point_min": 1,
                            },
                            {
                                "name": "Kiên nhẫn",
                                "description": "- Kiên nhẫn trong việc lắng nghe \n- Kiên nhẫn tìm tòi học hỏi",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": "-Kiên nhẫn giao tiếp, thuyết phục với những người khác background với mình (ví dụ KH chưa biết gì về CDP, phải nói chuyện đào tạo khái niệm cơ bản để có thể giao tiếp xa hơn được). Tiến tới giao tiếp với người khác mình về tính cách quan điểm (khác sóng), mà vẫn đi tiếp được để đạt được mục tiêu: Hiểu đúng, đồng thuận đúng để làm việc. ",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"-  Kiên nhẫn trong việc giao tiếp (tức nói) cho người khác hiểu với nhân sự nội bộ công ty, member của các team khác, với những người quen thuộc hoặc đồng điều với mình (tức cùng sóng, cùng tính). Việc kiên nhẫn giao tiếp cover cả việc nói đi nói lại, lặp đi lặp lại 1 chủ để nếu người nghe là người mới. \n\n- Kiên nhẫn trong việc học và tìm hiểu sản phẩm, các kiến thức cần thiết cho công việc này. Dù sao sản phẩm của Mobio cũng là một phần mềm mang tính giải pháp và tương đối phức tạp. Không đặt kỳ vọng sai trong việc xác định thời gian đào tạo & nắm bắt kiến thức."',
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"Kiên nhẫn giao tiếp, thuyết phục với những người khác background với mình (ví dụ KH chưa biết gì về CDP, phải nói chuyện đào tạo khái niệm cơ bản để có thể giao tiếp xa hơn được). Tiến tới giao tiếp với người khác mình về tính cách quan điểm (khác sóng), mà vẫn đi tiếp được để đạt được mục tiêu: Hiểu đúng, đồng thuận đúng để làm việc. \n\nở level 3 thì việc học hỏi cần kiên nhẫn với những chủ đề mà có thể mất nhiều tháng mới có thể hiểu được."',
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 4,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 5,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 3,
                                    },
                                ],
                                "pask_code": "personality",
                                "reference_info": None,
                                "is_default_apply": False,
                                "competency_default_id": None,
                                "competency_reference_id": None,
                                "competency_id": "bcd0f361a157437190d47339",
                                "point_min": 1,
                            },
                        ],
                        "is_default": False,
                        "competency_group_default_id": None,
                        "competency_group_id": "ddc493c7784340bfb15fd8b4",
                    },
                    {
                        "name": "Nhóm năng lực kiến thức",
                        "lst_competency": [
                            {
                                "name": "Tổng quan về Sản phẩm",
                                "description": "Kiến thức tổng quan về sản phẩm:\n- CDP & CEM \n- KH của Mobio là ai? Có đặc điểm gì?\n- Giá trị mà giải pháp mang lại cho KHDN ",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": '"- Hiểu cơ bản những kiến thức và thuật ngữ của phần mềm \n- Nắm bức tranh tổng quan mối quan hệ giữa các module nghiệp vụ\n- Nắm được toàn bộ ý nghĩa, nghiệp vụ của các module"',
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Nắm được chức năng một số giải pháp tương tự hoặc liên quan tới sản phẩm của Mobio, có khả năng phân biệt (VD CRM vs CDP), có khả năng đánh giá ưu nhược điểm và so sánh Mobio với các sản phẩm của đối thủ (VD so sánh Mobio vs Insider).",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Hiểu và nắm được định hướng Sản phẩm (Tổng Thể) của giải pháp của công ty. Phân biệt và hiểu rõ được sự khác biệt giữa phần mềm đơn lẻ (tool) với giải pháp tổng thể. Điều này dẫn đến việc khi làm việc trên 1 modules, một tính năng thì luôn phải hiểu được modules đó, tính năng đó đứng trong tổng thể giải pháp như thế nào. Khi thay đổi, cải tiến, phát triển tính năng của 1 modules thì ảnh hưởng liên đới tới các modules/tính năng khác ra sao. ",
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '" - So sánh chuyên sâu được về điểm mạnh, điểm yếu của từng tính năng, đến giải pháp của công ty với các đối thủ có trên thị trường. \n\nLưu ý: đây là so sánh chuyên sâu, đến mức có thể cài thầu, có thể tranh luận phản biện trực tiếp với những người ủng hộ sản phẩm đối thủ, hoặc thậm trí tranh luận phản biện trực tiếp với đối thủ. "',
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "Hiểu được, nhìn được vision sản phẩm với các bài toán Ứng Dụng và Kinh Doanh. Nói ngắn gọn là có Busines Sense (nhạy cảm kinh doanh) về sản phẩm!",
                                        "level": 5,
                                        "is_activate": True,
                                        "point_min": 5,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 4,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 5,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "knowledge",
                                "reference_info": "Xem sản phẩm Mobio",
                                "is_default_apply": False,
                                "competency_default_id": None,
                                "competency_reference_id": None,
                                "competency_id": "76b5f522b81742249147a5ff",
                                "point_min": 0,
                            },
                            {
                                "name": "Kiến thức liên quan đến nghiệp vụ của KH",
                                "description": "Kiến thức nghiệp vụ của ngành của KH, hay ngạch sản phẩm Mobio nhắm tới như \n- Nghiệp vụ chung ngành Marketing, Sales, Customer Service\n- Nghiệp vụ của KH Bank, Bán lẻ",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": '"- Biết và hiểu các thuật ngữ trong ngành \n- Quy trình vận hành công việc của MKT, Sale, CS"',
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Hiểu sâu công việc, nhiệm vụ và vai trò nghiệp vụ của MKT, Sale, CS\n- Mô hình kinh doanh của sản phẩm/ dịch vụ liên quan "',
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Nắm được insight đặc điểm cho ngành\n+ Các best practice mà người ta hay làm trong ngành này\n+ Những thứ khó khăn, paint point nhất của ngành này\n+ Những quy định trong ngành\n+ Và những quy trình nghiệp vụ cơ bản phải có trong ngành.\n+ Hay thậm chí, là những thủ thuật chui rút, đường tắt (hợp lệ hay không thì chưa biết) mà thị trường hay làm"',
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Đủ khả năng tư vấn cho chính khách hàng về kinh nghiệm triển khai, ứng dụng cho các hoạt động thuộc về MKT, Sale, Service trên Ngành của chính KH. \n\nLưu ý: Khách hàng ở đây cần phân biệt giữa Account (doanh nghiệp) và cá nhân (nhân viên của doanh nghiệp). Nhiều cá nhân la nhân viên của doanh nghiệp còn trẻ, còn thiếu hiểu biết về chính ngành họ đang làm, khi đi thu thập thông tin về yêu cầu phát triển phần mềm mà cứ nghe theo những nhân viên dạng này đặt ra đề bài, câu hỏi, yêu cầu rồi về răm răp làm theo là ""tự đào hố chôn mình"". \n\nMột level senior của hiểu biết/kiến thức nghiệp vụ là không chỉ hiểu, mà còn tư vấn, hướng dẫn ngược lại cho chính KH của mình (những người ở level Junior, Mid) nên làm gì, nên đặt đề bài/nhu cầu như thế nào là hơp lý. "',
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": " - Đủ khả năng nói chuyện, giao tiếp tư vấn được 3 tầng nhân sự của phía Doanh Nghiệp KH về nghiệp vụ (có thể chọn ngách nhánh ví dụ Hoặc Bank, Hoặc Retail, Hoặc Hostapility...chứ không ai yêu cầu phải hiểu biết mọi ngành); 3 tầng nhân sự đó gồm: Chuyên viên vận hành, Quản lý cấp giữa, và Giám đốc các trung tâm (ví dụ CMO, Sale Director, CX Director). ",
                                        "level": 5,
                                        "is_activate": True,
                                        "point_min": 5,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 4,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "knowledge",
                                "reference_info": None,
                                "is_default_apply": False,
                                "competency_default_id": None,
                                "competency_reference_id": None,
                                "competency_id": "d4ce2066516d4c2a8eb406f4",
                                "point_min": 0,
                            },
                            {
                                "name": "Know-how qua các dự án để tư vấn KH",
                                "description": "Đi vào dự án, nhu cầu của KH sẽ rất nhiều và dồn dập. Có những KH cũng không xác định cái mình CẦN nên dựa vào sự va chạm với ở các dự án tương tự sẽ giúp cho BA có kiến thức và cơ sở để trao đổi - tư vấn ngược lại KH sát vấn đề/cái họ cần. \nViệc này sẽ đò",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": "",
                                        "level": 1,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 2,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Có thể nghe - hiểu đúng tình huống của KH\n- Chủ động tìm hiểu các kịch bản/ usecase ở các dự án đã chạy"',
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Đã có giờ bay ở nhiều dự án\n- Nắm được các tình huống, kịch bản ứng dụng thực tế tương tự để đưa ra đánh giá cơ bản về việc nên hay không nên, làm cách nào hiệu quả nhất. "',
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Đủ khả năng lập luận chặt chẽ và logic kèm tình huống thực tế để đàm phán - thuyết phục KH theo phương án mong muốn Mobio đưa ra",
                                        "level": 5,
                                        "is_activate": True,
                                        "point_min": 5,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 4,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 5,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 4,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 5,
                                    },
                                ],
                                "pask_code": "knowledge",
                                "reference_info": None,
                                "is_default_apply": False,
                                "competency_default_id": None,
                                "competency_reference_id": None,
                                "competency_id": "8d316634704945d6a70abe1b",
                                "point_min": 0,
                            },
                            {
                                "name": "Kiến thức về cơ sở dữ liệu, SQL queries, data mapping",
                                "description": "",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": '"- Mối quan hệ giữa các bảng dữ liệu\n- Biết làm và đánh giá data model (data sample, data table, data type)\n- Đọc hiểu cơ bản doc API của Dev"',
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Biết truy vấn dữ liệu khi cần\n-  Vẽ và xác định được mối quan hệ dữ liệu các bảng và thuộc tính."',
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 3,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 4,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 5,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 2,
                                    },
                                ],
                                "pask_code": "knowledge",
                                "reference_info": "Tham khảo khoá học về CSDL, SQL...",
                                "is_default_apply": False,
                                "competency_default_id": None,
                                "competency_reference_id": None,
                                "competency_id": "016c6afee0604c17a3bd9922",
                                "point_min": 0,
                            },
                        ],
                        "is_default": False,
                        "competency_group_default_id": None,
                        "competency_group_id": "1bb9f6cb884643c5a7f0e010",
                    },
                    {
                        "name": "Nhóm năng lực kỹ năng làm việc",
                        "lst_competency": [
                            {
                                "name": "Giao tiếp",
                                "description": "Khả năng diễn đạt, truyền đạt",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": '"- Giao tiếp thụ động.\n- Nhận thông tin 1 chiều; không có khả năng phát triển, khai thác thông tin trong quá trình giao tiếp."',
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Giao tiếp có chủ động nhưng chưa rõ ý.\n- Làm các thành viên khác dễ hiểu nhầm hoặc miss thông tin."',
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Giao tiếp rõ ràng, dễ hiểu cả nói và viết.\n- Đảm bảo thông tin đầy đủ, chính xác."',
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Chủ động giao tiếp một cách rõ ràng và dễ hiểu.\n- Đảm bảo thông tin đầy đủ, chính xác.\n- Có khả năng gợi mở, phát triển và khai thác thêm thông tin trong quá trình giao tiếp."',
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 5,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 4,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "skill",
                                "reference_info": "Tham khảo khoá học giao tiếp X, link_https://test16.mobio.vn/competency-framework",
                                "is_default_apply": False,
                                "competency_default_id": None,
                                "competency_reference_id": None,
                                "competency_id": "a6521a997d2f45a39693c534",
                                "point_min": 2,
                            },
                            {
                                "name": "Giải quyết vấn đề",
                                "description": "",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": '"- Có khó khăn trong việc nhận ra các vấn đề quan trọng; mất tập trung vào các vấn đề lớn.\n- Chủ động cập nhật tình hình cho người quản lý và các thành viên liên quan.\n- Thông thường không đưa ra được giải pháp cho các vấn đề phát sinh.\n- Cần người trợ giúp để tìm ra phương án giải quyết vấn đề."',
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Nhận biết được vấn đề.\n- Chủ động cập nhật tình hình cho người quản lý và các thành viên liên quan.\n- Cần hỗ trợ trong việc phân tích và ưu tiên các vấn đề; có xu hướng tập trung vào các vấn đề đơn giản.\n- Các quyết định đôi khi không rõ ràng."',
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Phân loại được mức độ ưu tiên và mức độ ảnh hưởng của các vấn đề.\n- Chủ động cập nhật tình hình cho người quản lý và các thành viên liên quan.\n- Chủ động phân tích khi nhận thấy có vấn đề tồn tại và đang cần xử lý gấp.\n- Ra quyết định dựa trên thực tế và kinh nghiệm."',
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Đánh giá chính xác toàn bộ về nguyên nhân, mức độ ưu tiên và mức độ nghiêm trọng của vấn đề.\n- Bình tĩnh, đưa ra phương án xử lý vấn đề.\n- Chuẩn bị các phương án dự phòng trong trường hợp ko xử lý được."',
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Đánh giá và đưa ra quyết định với những vấn đề nghiêm trọng, sau khi đã tham khảo ý kiến của những người tham gia.\n- Đưa ra được nhiều giải pháp thay thế để lựa chọn.\n- Chuẩn bị các phương án dự phòng trong trường hợp ko xử lý được.\n- Thường xuyên đưa ra các quyết định rõ ràng, minh bạch, kịp thời; các quyết định nhất quán với các mục tiêu của tổ chức."',
                                        "level": 5,
                                        "is_activate": True,
                                        "point_min": 5,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 4,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "skill",
                                "reference_info": None,
                                "is_default_apply": False,
                                "competency_default_id": None,
                                "competency_reference_id": None,
                                "competency_id": "806b95b9d2bb42b5907eea64",
                                "point_min": 2,
                            },
                            {
                                "name": "Làm việc nhóm ",
                                "description": 'Nguyên tắc của làm việc nhóm:\n- Tôn trọng: tuân thủ kế hoạch, quyết định nhóm\n- Đóng góp: Chủ động đưa ý kiến có tính xây dựng, sự sáng tạo và những ""viên gạch"" khác để giúp nhóm hoạt động tốt hơn.\n- Cởi mở: Đón nhận các ý kiến trái chiều\n- Hợp tác: Đặt',
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": '"- Có khả năng nhận biết rõ vai trò, trách nhiệm, phạm vi công việc của từng thành viên trong team và mối quan hệ giữa công việc của mình và team.\n- Sẵn sàng trao đổi, hỗ trợ và phối hợp với các thành viên khác trong quá trinhg làm việc.\n- Hợp tác tốt với hầu hết các thành viên.\n- Nhận diện được tình huống mâu thuẫn (conflict) và giữ được thái độ bình tĩnh, khách quan, đóng góp trong quá trình xử lý mâu thuẫn.\n- Tách biệt cảm xúc và vấn đề cá nhân vs công việc. Đặt mục tiêu của team lên trên mục tiêu của cá nhân."',
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Nhận biết được đặc điểm, điểm mạnh và điểm yếu của từng thành viên và biết cách phối hợp sao cho nhịp nhàng, phù hợp.\n- Có khả năng phối hợp được với các thành viên, bất kể background, tính cách, giới tính,...\n- Có khả năng nhận diện và xử lý được mâu thuẫn trong team, khơi gợi sự hợp tác giữa các thành viên.\n- Có thể đảm nhiệm tốt vai trò dẫn dắt team work."',
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Có thể đảm nhiệm vai trò dẫn dắt (leader) trong teamwork một cách hiệu quả, bao gồm việc lên kế hoạch, ra quyết định hoặc dẫn dắt việc ra quyết định nhóm, tạo ra môi trường làm việc nhóm lành mạnh, hiệu quả, đảm bảo cả nhóm đồng lòng với quyết định/mục tiêu chung...\n- Có khả năng đánh giá kết quả làm việc của team một cách khách quan, bảo vệ kết quả và điều phối việc phối hợp với các team khác.\n- Có cái nhìn rộng hơn, đặt mục tiêu nhóm align với mục tiêu chung của cả công ty."',
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 4,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": '"- Phân biệt được công việc động lập và công việc cần teamwork.\n- Chủ động và nghiêm túc thực hiện công việc của mình.\n- Ít có sự đóng góp cho công việc chung (trong nội bộ team và nội bộ công ty)."',
                                        "level": 4,
                                        "is_activate": True,
                                        "point_min": 5,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 5,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": 4,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 3,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 4,
                                    },
                                ],
                                "pask_code": "skill",
                                "reference_info": None,
                                "is_default_apply": False,
                                "competency_default_id": None,
                                "competency_reference_id": None,
                                "competency_id": "d49dfefec2d14416ad10648f",
                                "point_min": 2,
                            },
                            {
                                "name": "Khả năng thích ứng",
                                "description": "Khả năng thích ứng với những cái mới\n- Thích ứng trong 1 môi trường làm việc, đồng nghiệp khắt khe\n- Khả năng ứng biến với sự thay đổi KH hay phong cách làm việc của mỗi 1 môi trường đối tác khác nhau.",
                                "weight": 1,
                                "behavior_expressions": [
                                    {
                                        "description": "- Thích nghi trong môi trường làm việc\n- Thích nghi với tính cách khác biệt hay sự khắt khe và chỉn chu của cộng sự để mỗi bản thân nâng cấp năng lực của chính mình ",
                                        "level": 1,
                                        "is_activate": True,
                                        "point_min": 1,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "-  Thích nghi với áp lực khi làm việc với KH khó tính\n-  Có quan sát người khác xử lý và học hỏi để bản thân ứng dụng\n- Là người bình tĩnh, cố gắng nhìn nhận vấn đề trực diện, ít bán than",
                                        "level": 2,
                                        "is_activate": True,
                                        "point_min": 2,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "- Có khả năng thích nghi với môi trường đối tác. Xác định được vai trò của từng thành viên trong dự án. Để trao đổi dễ dàng hơn.\n - Thích nghi với những thay đổi bất ngờ từ đối tác. Kiên nhẫn giải quyết để dự án không bị ảnh hưởng",
                                        "level": 3,
                                        "is_activate": True,
                                        "point_min": 3,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 4,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                    {
                                        "description": "",
                                        "level": 5,
                                        "is_activate": False,
                                        "point_min": 0,
                                        "point_max": 0,
                                        "point_mid": 0,
                                    },
                                ],
                                "job_title_levels": [
                                    {
                                        "job_title_level_id": "d33e870e-8aed-4a17-b1ed-6c10e75f79be",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "5cb4cace-5893-44f5-a3c6-660243c33e0c",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "adf9f95e-e256-4d91-bca8-92c2bc189bc0",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "60df6e66-aa6b-48d0-8192-2b80f003634e",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "58503337-7807-4cb0-9acf-cb3ba43dc1ef",
                                        "behavior_expression_level": -1,
                                    },
                                    {
                                        "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                                        "behavior_expression_level": 1,
                                    },
                                    {
                                        "job_title_level_id": "482cee30-1b09-4e17-8510-b98c60b24095",
                                        "behavior_expression_level": 2,
                                    },
                                    {
                                        "job_title_level_id": "26805237-8ad1-4797-ba08-99cec689b985",
                                        "behavior_expression_level": 3,
                                    },
                                ],
                                "pask_code": "personality",
                                "reference_info": None,
                                "is_default_apply": False,
                                "competency_default_id": None,
                                "competency_reference_id": None,
                                "competency_id": "2abeffcbc7934591b93da849",
                                "point_min": 1,
                            },
                        ],
                        "is_default": False,
                        "competency_group_default_id": None,
                        "competency_group_id": "555cfa6b988b4791bbbfe4db",
                    },
                ],
                "competency_framework_id": "67f73b1ce8711af2623bbb1c",
                "time_eval_of_users": [
                    {
                        "user_id": "b2c2275c-1f97-47ff-a577-f8e8b4c9f687",
                        "start_time": "2025-06-16T17:01Z",
                        "end_time": "2025-06-19T16:59Z",
                        "user_type": 1,
                    },
                    {
                        "user_id": "72e85e0e-ffa3-455f-a857-a9431da482c9",
                        "start_time": "2025-06-19T16:59Z",
                        "end_time": "2025-06-22T16:59Z",
                        "user_type": 2,
                    },
                ],
                "status": 3,
                "disable_edit": False,
                "before_job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                "after_job_title_level_id": "",
                "amount_of_work_note": "",
                "user_note": "",
                "hr_review": "",
                "leader_review": "",
                "work_process_note": "",
                "created_by": "admin",
                "updated_by": "admin",
                "updated_time": "2025-06-17T06:57Z",
                "created_time": "2025-06-17T06:57Z",
                "status_filter": "waiting_user",
                "job_title": "QC",
                "job_title_id": "863d2e05-c0a0-4d79-9411-289dab93ceee",
                "job_title_level_id": "88201730-4d35-4cf8-8d5d-3efd7ea949dd",
                "job_title_level": "Midde",
                "department": "Test Manual",
                "id": "685111c8d2e84c1a6213cd57",
            },
        }

    evaluate = x.get("evaluate")
    competency_groups = evaluate.get("competency_groups")
    total_point = 0
    for competency_group in competency_groups:
        for competency in competency_group.get("lst_competency"):
            total_point += competency.get("point_min")
    print(total_point)