version: "3"

services:
  pgdb:
    container_name: postgres_ladder_db
    image: postgres:11.5-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=root
      - POSTGRES_PASSWORD=root
      - PGDATA=/var/lib/postgresql/data/ladder/
    volumes:
      - ./postgres-data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql

  # pgadmin:
  #   image: dpage/pgadmin4
  #   restart: always
  #   ports:
  #     - "9000:80"
  #   environment:
  #     PGADMIN_DEFAULT_EMAIL: <EMAIL>
  #     PGADMIN_DEFAULT_PASSWORD: Mo<PERSON>@123


  mongodb:
    container_name: mongo_ladder_db
    image: mongo
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: root
    ports:
      - "27017:27017"
    volumes:
      - ./mongo-data:/data/db